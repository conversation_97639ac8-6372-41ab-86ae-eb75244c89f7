# Base Stage [COR1001] using official image for Go [COR1007]
ARG TARGET_DOCKER_PLATFORM
FROM --platform=${TARGET_DOCKER_PLATFORM:-linux/amd64} golang:1.18 AS base

# Shell option to fail pipes according to https://github.com/hadolint/hadolint/wiki/DL4006 recommendation [COR4001]
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

ARG CI_DEPLOY_TOKEN
RUN go env -w GOPRIVATE=gitlab.happyfresh.net && \
    echo -e "machine gitlab.happyfresh.net\nlogin catalog-deploy-token-1\npassword ${CI_DEPLOY_TOKEN}" > ~/.netrc
    

# Define all OS package dependencies and use version numbers [COR1008]
RUN apt-get update && apt-get install -y --no-install-recommends \
    unzip \
    && rm -rf /var/lib/apt/lists/*

ENV CGO_ENABLED 1
ENV GOOS linux

# Define extra software / library dependencies not managed by the OS package manager
WORKDIR /tmp
# Install Protobuf (gogo fork)
RUN curl -OL https://github.com/gogo/protobuf/archive/v1.3.0.zip \
    && unzip v1.3.0.zip \
    && mv /tmp/protobuf-1.3.0 /usr/local/include

# Install Protoc (architecture-aware)
RUN if [ "$(uname -m)" = "x86_64" ]; then \
        curl -L https://github.com/protocolbuffers/protobuf/releases/download/v3.20.3/protoc-3.20.3-linux-x86_64.zip -o protoc.zip; \
    elif [ "$(uname -m)" = "aarch64" ]; then \
        curl -L https://github.com/protocolbuffers/protobuf/releases/download/v3.20.3/protoc-3.20.3-linux-aarch_64.zip -o protoc.zip; \
    else \
        echo "Unsupported architecture: $(uname -m)" && exit 1; \
    fi && \
    unzip protoc.zip -d /tmp/protoc3 && \
    mv /tmp/protoc3/bin/* /usr/local/bin/ && \
    mv /tmp/protoc3/include/* /usr/local/include/

RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.28.0
RUN go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.3.0

# Install GRPCurl for healthchecking (architecture-aware)
# To solve bitbucketpipeline userid error during builds, using mkdir
RUN mkdir -p /opt/app
RUN if [ "$(uname -m)" = "x86_64" ]; then \
        curl -L https://github.com/fullstorydev/grpcurl/releases/download/v1.9.2/grpcurl_1.9.2_linux_x86_64.tar.gz -o grpcurl.tar.gz; \
    elif [ "$(uname -m)" = "aarch64" ]; then \
        curl -L https://github.com/fullstorydev/grpcurl/releases/download/v1.9.2/grpcurl_1.9.2_linux_arm64.tar.gz -o grpcurl.tar.gz; \
    else \
        echo "Unsupported architecture: $(uname -m)" && exit 1; \
    fi && \
    tar -xzf grpcurl.tar.gz && \
    cp grpcurl /usr/local/bin/ && \
    cp grpcurl /opt/app

WORKDIR $GOPATH/src/happyfresh.io/product-promotion
CMD ["/bin/sh"]


# Development Stage (meant to be used with volume bind mount) [COR1002]
FROM base AS dev
# Define any other development environment tools / software if any
# (For example, using reflex for hot-reload)
RUN go install github.com/cespare/reflex@v0.3.1
# Define a custom file watcher to automatically run a hot-reload server
# Use this if using vendoring
#CMD ["/go/bin/reflex", "-s", "--", "go", "run", "-mod=vendor", "./cli/calculator.go", "server"]
# Use this if not using vendoring
CMD ["/go/bin/reflex", "-s", "--", "go", "run", "./cli/aloha.go", "server"]

# Testing Stage [COR1003]
FROM base AS test
COPY . .
ENV GOSUMDB=off
RUN make bindata
RUN go mod tidy
RUN go mod download
# Harusnya "RUN go test ./..." tapi ada test yang fail sementara ini
# RUN go test -run ''

# Pre-Production Stage [COR1004]
FROM test AS pre-prod
# Define build commands and output
# Cannot run make build because it uses -race flag (enables CGO)
# See https://stackoverflow.com/questions/34729748/installed-go-binary-not-found-in-path-on-alpine-linux-docker
RUN mkdir -p /opt/app
RUN go build -race -o /opt/app/aloha cli/aloha.go
RUN go build lib/rpc/client/client.go lib/rpc/client/conn.go
#RUN cp ./resources/deploy/production/psrv /opt/app/output

# Production Stage [COR1005] using minimal OS version [COR1006] and official alpine image [COR1007]
FROM --platform=${TARGET_DOCKER_PLATFORM:-linux/amd64} debian:10.7-slim AS prod

ARG VERSION

# DataDog
ENV DD_VERSION=$VERSION
ENV DD_SOURCE="go"
ENV GODEBUG=profstackdepth=32
LABEL com.datadoghq.tags.version=$VERSION
LABEL com.datadoghq.tags.source="${DD_SOURCE}"

# Define all OS package dependencies and use version numbers [COR1008]
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates=20200601~deb10u2 \
    mime-support=3.62 \
    && rm -rf /var/lib/apt/lists/*

# Copy GPRCurl for healthcheck
COPY --from=base /opt/app/grpcurl /bin

# Create a restricted user for use in production server [COR4002]
RUN groupadd -r appuser
RUN useradd -r -s /bin/false -g appuser appuser

USER appuser
# Using /opt/app as working dir [COR3002]
WORKDIR /opt/app
COPY --chown=appuser:appuser --from=pre-prod /opt/app/aloha .


EXPOSE 9342
# Healthcheck [COR3003]
HEALTHCHECK --interval=30s --timeout=5s --retries=2 --start-period=10s CMD grpcurl -v -plaintext localhost:9342 aloha.promo.Api/Ping || exit 1

ENTRYPOINT [ "./aloha" ]
CMD [ "server" ]


# Production Stage [COR1005] using minimal OS version [COR1006] and official alpine image [COR1007]
FROM --platform=${TARGET_DOCKER_PLATFORM:-linux/amd64} debian:10.7-slim AS prod-worker

# Define all OS package dependencies and use version numbers [COR1008]
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates=20200601~deb10u2 \
    mime-support=3.62 \
    && rm -rf /var/lib/apt/lists/*

ENV GODEBUG=profstackdepth=32

# Copy GPRCurl for healthcheck
COPY --from=base /opt/app/grpcurl /bin

# Create a restricted user for use in production server [COR4002]
RUN groupadd -r appuser
RUN useradd -r -s /bin/false -g appuser appuser

USER appuser
# Using /opt/app as working dir [COR3002]
WORKDIR /opt/app
COPY --chown=appuser:appuser --from=pre-prod /opt/app/aloha .


EXPOSE 9342
# Healthcheck [COR3003]
HEALTHCHECK --interval=30s --timeout=5s --retries=2 --start-period=10s CMD grpcurl -v -plaintext localhost:9342 aloha.promo.Api/Ping || exit 1

ENTRYPOINT [ "./aloha" ]
CMD [ "worker" ]
