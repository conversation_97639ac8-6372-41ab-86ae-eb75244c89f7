package config

import (
	"io/ioutil"
	"strings"

	"github.com/spf13/viper"
	"happyfresh.io/lib/log"
)

var (
	c    *viper.Viper
	path = []string{
		"$GOPATH/src/happyfresh.io/product-promotion/",
		"/var/app/current",
		".",
	}
)

// Env :nodoc:
func Env() string {
	return c.GetString("env")
}

// DatabaseDSN :nodoc:
func DatabaseDSN() string {
	return c.GetString("db_dsn")
}

// DatabaseRODSNs :nodoc:
func DatabaseRODSNs() []string {
	dsnStr := c.GetString("db_ro_dsn")
	if dsnStr == "" {
		return []string{}
	}

	return strings.Split(dsnStr, "|")
}

// DatabaseMaxOpenConnection :nodoc:
func DatabaseMaxOpenConnection() int {
	return c.GetInt("db_max_open_conn")
}

// DatabaseMaxIdleConnection :nodoc:
func DatabaseMaxIdleConnection() int {
	return c.GetInt("db_max_idle_conn")
}

// SentryDSN :nodoc:
func SentryDSN() string {
	return c.GetString("sentry_dsn")
}

// NewRelicKey :nodoc:
func NewRelicKey() string {
	return c.GetString("newrelic_key")
}

// Version :nodoc:
func Version() string {
	return c.GetString("version")
}

// KafkaSASLUsername :nodoc:
func KafkaSASLUsername() string {
	return c.GetString("kafka_sasl_username")
}

// KafkaSASLPassword :nodoc:
func KafkaSASLPassword() string {
	return c.GetString("kafka_sasl_password")
}

// KafkaBrokers :nodoc:
func KafkaBrokers() string {
	return c.GetString("kafka_brokers")
}

// KafkaGroupID :nodoc:
func KafkaGroupID() string {
	return c.GetString("kafka_group_id")
}

// KafkaTopics :nodoc:
func KafkaTopics() string {
	return c.GetString("kafka_topics")
}

// KafkaPublisherPrefix :nodoc:
func KafkaPublisherPrefix() string {
	return c.GetString("kafka_publisher_prefix")
}

func WebHookMY() string {
	return c.GetString("webhook_promotion_my")
}

func WebHookTH() string {
	return c.GetString("webhook_promotion_th")
}

func WebHookID() string {
	return c.GetString("webhook_promotion_id")
}

// KafkaSASLMechanism :nodoc:
func KafkaSASLMechanism() string {
	return c.GetString("kafka_sasl_mechanism")
}

func init() {
	c = viper.New()
	for _, p := range path {
		c.AddConfigPath(p)
	}
	c.SetConfigName("config")
	c.SetDefault("env", "development")
	c.SetDefault("db_max_open_conn", 5)
	c.SetDefault("db_max_idle_conn", 1)
	c.SetDefault("kafka_sasl_mechanism", "PLAIN")

	if version, err := ioutil.ReadFile("Version"); err == nil && len(string(version)) > 0 {
		c.Set("version", string(version))
	}

	c.SetEnvPrefix("PSRV")
	c.SetConfigName("config")

	c.AutomaticEnv()
	err := c.ReadInConfig()
	if err != nil {
		log.For("config", "init").Infof("Reading Config Error %v \n Only use automatic ENV", err)
	}

	log.SetLevel(log.DebugLevel)
	log.SetFormat(log.JSONFormat)
}
