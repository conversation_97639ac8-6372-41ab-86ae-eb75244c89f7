# Catalog Service - Promotion

## Prerequisite


1. Go ~1.11.0
2. PostgreSQL 9
3. GRPC + Protobuf
4. [Dep](https://github.com/golang/dep)
5. [Golang Migrate](https://github.com/golang-migrate/migrate)

## Development

### Resolving dependencies

+ To resolve all dependencies stated in `Gopkg.lock` file, run

   ```bash
   dep ensure
   ```

+ To add a new dependency to the project, run

   ```bash
   dep ensure -add ${DEPENDENCY_URL}
   ```

### Updating DB

+ To generate new DB migration file, run

   ```bash
   migrate create -dir resources/sql/migration/ -ext sql ${FILE_NAME}
   ```

   Please follow these conventions in naming the file:

  + `create-table-${TABLE_NAME}` to create table
  + `add-column-${COLUMN_NAME}-to-${TABLE_NAME}` to add column

   Remember to always put the migration file in `.resources/sql/migration/` directory

+ To run migration

  + to up

      ```bash
      go run cli/aloha.go migrate up
      ```

  + to down

      ```bash
      go run cli/aloha.go migrate down
      ```

  + to go to certain version

      ```bash
      go run cli/aloha.go migrate do ${VERSION}
      ```

   The commands above will use DB URL set in environment variable or `config.yml`.

   You can also use the original Go Migrate command like below.

   ```bash
   migrate -path resources/sql/migration/ -database "${DB_DSN}" up
   ```
