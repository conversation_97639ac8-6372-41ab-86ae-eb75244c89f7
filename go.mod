module happyfresh.io/product-promotion

go 1.18

require (
	github.com/Masterminds/squirrel v1.5.2
	github.com/Shopify/sarama v1.28.0
	github.com/XSAM/otelsql v0.12.0
	github.com/aws/aws-sdk-go v1.34.28
	github.com/fitraditya/interlog v0.0.0-20200618053439-c13b0d0a76af
	github.com/golang-migrate/migrate v3.5.4+incompatible
	github.com/golang/protobuf v1.5.2
	github.com/grpc-ecosystem/go-grpc-middleware v1.3.0
	github.com/lib/pq v1.10.2
	github.com/newrelic/go-agent v3.15.2+incompatible
	github.com/nleof/goyesql v1.0.1
	github.com/pkg/errors v0.9.1
	github.com/sony/gobreaker v0.5.0
	github.com/spf13/cobra v0.0.3
	github.com/spf13/viper v1.7.2-0.20201030221906-f415025b98ce
	github.com/stretchr/testify v1.8.1
	github.com/xdg/scram v0.0.0-20180814205039-7eeb5667e42c
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.42.0
	go.opentelemetry.io/otel v1.16.0
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.16.0
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.16.0
	go.opentelemetry.io/otel/sdk v1.16.0
	golang.org/x/sync v0.0.0-20220722155255-886fb9371eb4
	google.golang.org/grpc v1.56.3
	gopkg.in/DataDog/dd-trace-go.v1 v1.56.1
	happyfresh.io/lib v0.5.3
)


