package main

import (
	"os"

	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/cli/migrate"
	"happyfresh.io/product-promotion/cli/server"
	"happyfresh.io/product-promotion/cli/workers"

	"github.com/spf13/cobra"
)

var (
	alohaCmd = &cobra.Command{
		Use: "aloha",
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Usage()
		},
	}
)

func main() {
	alohaCmd.AddCommand(
		server.ServerCmd,
		workers.WorkerCmd,
		migrate.Cmd,
	)
	if err := alohaCmd.Execute(); err != nil {
		log.For("main", "main").Error(err.Error())
		os.Exit(-1)
	}
}
