package main

import (
	"bytes"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/elasticbeanstalk"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"happyfresh.io/lib/log"
)

var (
	rootCmd = &cobra.Command{
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Usage()
		},
	}

	deployCmd = &cobra.Command{
		Use:   "deploy",
		Short: "Deploy web application to AWS Elastic Beanstalk",
		Args: func(cmd *cobra.Command, args []string) error {
			valid := true
			if len(args) < 1 {
				valid = false
			} else if args[0] == "" {
				valid = false
			}
			if !valid {
				return errors.New("Argument is invalid")
			}
			return nil
		},
	}

	printEnvCmd = &cobra.Command{
		Use:   "env",
		Short: "Get deployment environment by Git branch",
	}

	cfg = viper.New()
)

func deploy(cmd *cobra.Command, args []string) error {
	appName := "CatalogService-Promotion"
	appEnv := cfg.GetString("application_environment")
	appVersion := cfg.GetString("application_version")
	appBucketKey := fmt.Sprintf("%s/%s-%s-%s.zip", appName, appEnv, appVersion, time.Now().Format("20060102150405"))
	if ok, _ := cmd.PersistentFlags().GetBool("print_env"); ok {
		_, err := fmt.Fprint(os.Stdout, appEnv)
		return err
	}

	log.For("cmd", "deploy").Infof("Preparing deployment of %s version %s on %s...", appName, appVersion, appEnv)
	awsCfg := &aws.Config{
		Region: aws.String(cfg.GetString("aws_region")),
	}
	awsSession, err := session.NewSession(awsCfg)
	if err != nil {
		return err
	}

	log.For("cmd", "deploy").Infof("Uploading the artifact...")
	artifact, err := ioutil.ReadFile(cfg.GetString("artifact"))
	if err != nil {
		return err
	}
	awsS3 := s3.New(awsSession, awsCfg)
	if _, err = awsS3.PutObject(&s3.PutObjectInput{
		Body:   bytes.NewReader(artifact),
		Bucket: aws.String(cfg.GetString("s3_bucket")),
		Key:    aws.String(appBucketKey),
	}); err != nil {
		return err
	}

	log.For("cmd", "deploy").Infof("Creating a new application version...")
	awsEB := elasticbeanstalk.New(awsSession, awsCfg)
	if _, err = awsEB.CreateApplicationVersion(&elasticbeanstalk.CreateApplicationVersionInput{
		ApplicationName: aws.String(appName),
		VersionLabel:    aws.String(appVersion),
		SourceBundle: &elasticbeanstalk.S3Location{
			S3Bucket: aws.String(cfg.GetString("s3_bucket")),
			S3Key:    aws.String(appBucketKey),
		},
		Process: aws.Bool(true),
	}); err != nil {
		return err
	}

	log.For("cmd", "deploy").Infof("Waiting application version %s to be persisted...", appVersion)
	<-time.After(5 * time.Second)

	log.For("cmd", "deploy").Infof("Updating environment %s...", "psrv-"+appEnv)
	if _, err = awsEB.UpdateEnvironment(&elasticbeanstalk.UpdateEnvironmentInput{
		ApplicationName: aws.String(appName),
		EnvironmentName: aws.String("psrv-" + appEnv),
		VersionLabel:    aws.String(appVersion),
	}); err != nil {
		return err
	}

	return nil
}

func preDeploy(cmd *cobra.Command, args []string) error {
	cfg.SetDefault("artifact", args[0])
	cfg.SetDefault("s3_bucket", os.Getenv("AWS_BUCKET"))
	cfg.SetDefault("aws_region", os.Getenv("AWS_REGION"))
	cfg.BindPFlag("application_version", cmd.PersistentFlags().Lookup("application_version"))
	cfg.BindPFlag("application_environment", cmd.PersistentFlags().Lookup("application_environment"))
	cfg.SetEnvPrefix("PSRV")
	cfg.AutomaticEnv()
	cfg.ReadInConfig()

	return nil
}

func printEnv(cmd *cobra.Command, args []string) error {
	appEnv := ""
	if len(args) < 1 {
		appEnv = getEnv("")
	} else {
		appEnv = getEnv(args[0])
	}

	fmt.Print(appEnv)
	return nil
}

func getEnv(branch string) string {
	envMap := map[string]string{
		"master":  "production",
		"replica": "replica",
		"develop": "staging",
		"sandbox": "sandbox",
	}
	env, ok := envMap[branch]
	if !ok {
		log.For("cmd", "env").Infof("Environment for the branch cannot be found. Returning as \"develop\"")
		return envMap["develop"]
	}

	return env
}

func init() {
	printEnvCmd.RunE = printEnv

	deployCmd.RunE = deploy
	deployCmd.PreRunE = preDeploy
	deployCmd.PersistentFlags().StringP("application_environment", "e", "", "Set branch to deploy")
	deployCmd.PersistentFlags().StringP("application_version", "v", "", "Set version to deploy")
	deployCmd.MarkPersistentFlagRequired("application_environment")
	deployCmd.MarkPersistentFlagRequired("application_version")

	rootCmd.AddCommand(deployCmd, printEnvCmd)
}

func main() {
	if err := deployCmd.Execute(); err != nil {
		log.For("deploy", "main").Error(err.Error())
		os.Exit(-1)
	}
}
