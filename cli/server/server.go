package server

import (
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpcotel "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"

	"github.com/fitraditya/interlog"
	"github.com/spf13/cobra"
	"google.golang.org/grpc"
	health "google.golang.org/grpc/health/grpc_health_v1"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/api"
	"happyfresh.io/product-promotion/api/middleware"
	"happyfresh.io/product-promotion/config"
	"happyfresh.io/product-promotion/db"
	"happyfresh.io/product-promotion/lib/rpc"

	"google.golang.org/grpc/reflection"
)

// Command for running promotion service gRPC server
var (
	ServerCmd = &cobra.Command{
		Use:   "server",
		Short: "Run gRPC server",
		Long:  "Run promotion service gRPC server",
	}
	serverAddress string
)

func serverRunE(cmd *cobra.Command, args []string) error {
	log.Debug("Running gRPC server...")
	var srv *grpc.Server

	net, err := net.Listen("tcp", serverAddress)
	if err != nil {
		return err
	}

	// tp := oteltracer.InitTracer()
	// defer func() {
	// 	err := tp.Shutdown(context.Background())
	// 	if err != nil {
	// 		panic(err)
	// 	}
	// }()

	logrpc := interlog.New()
	logrpc.SetEnv(config.Env())

	if config.Env() == "production" || config.Env() == "staging" {
		host, err := getDataDogAgentHost()
		if err == nil {
			os.Setenv("DD_AGENT_HOST", host)
			tracer.Start(
				tracer.WithEnv(config.Env()),
				tracer.WithAgentAddr(host),
				tracer.WithService("CatalogService-Promotion"),
			)

			defer tracer.Stop()
		}
	}

	srv = grpc.NewServer(
		grpc.UnaryInterceptor(
			grpc_middleware.ChainUnaryServer(
				// oteltracer.ChainUnaryServerIntercepetor(true, true),
				logrpc.Unary(),
				middleware.GRPCError,
				middleware.LogError,
			),
		),
		grpc.StreamInterceptor(grpcotel.StreamServerInterceptor()),
	)

	rpc.RegisterApiServer(srv, &api.API{})
	health.RegisterHealthServer(srv, &api.API{})
	reflection.Register(srv)

	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM)
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		s := <-sigCh
		log.Printf("Got signal %v, attempting graceful shutdown", s)
		srv.GracefulStop()
		wg.Done()
	}()

	if err := srv.Serve(net); err != nil {
		log.Fatal(err)
	}

	wg.Wait()
	log.Printf("Shutted down")
	return nil
}

func beforeRunE(cmd *cobra.Command, args []string) error {
	return db.Open(config.DatabaseDSN(), config.DatabaseRODSNs()...)
}

func init() {
	/*
		fmt.Println(`
		 _____                           _   _                _____                 _
		|  __ \                         | | (_)              / ____|               (_)
		| |__) | __ ___  _ __ ___   ___ | |_ _  ___  _ __   | (___   ___ _ ____   ___  ___ ___
		|  ___/ '__/ _ \| '_ ' _ \ / _ \| __| |/ _ \| '_ \   \___ \ / _ \ '__\ \ / / |/ __/ _ \
		| |   | | | (_) | | | | | | (_) | |_| | (_) | | | |  ____) |  __/ |   \ V /| | (_|  __/
		|_|   |_|  \___/|_| |_| |_|\___/ \__|_|\___/|_| |_| |_____/ \___|_|    \_/ |_|\___\___|

		`)
	*/

	ServerCmd.RunE = serverRunE
	ServerCmd.PreRunE = beforeRunE
	ServerCmd.PersistentFlags().StringVarP(&serverAddress, "server", "s", "0.0.0.0:9342", "gRPC server address")
}

func getDataDogAgentHost() (string, error) {
	resp, err := http.Get("http://***************/latest/meta-data/local-ipv4")
	defer func() {
		err := resp.Body.Close()
		if err != nil {
			log.For("cmd", "get data dog agent host").Error(err)
		}
	}()

	if err != nil {
		log.For("cmd", "get data dog agent host").Error(err)
		return "", err
	}

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.For("cmd", "get data dog agent host").Error(err)
		return "", err
	}

	return string(bodyBytes), nil
}
