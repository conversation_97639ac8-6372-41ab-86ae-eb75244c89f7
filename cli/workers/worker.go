package workers

import (
	"context"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/Shopify/sarama"
	"github.com/spf13/cobra"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/config"
	"happyfresh.io/product-promotion/db"
	"happyfresh.io/product-promotion/lib/kafka"
	"happyfresh.io/product-promotion/lib/kafka/handler"
	"happyfresh.io/product-promotion/lib/worker"
)

var (
	WorkerCmd = &cobra.Command{
		Use:   "worker",
		Short: "Run worker",
		Long:  "Run catalog service worker",
	}
)

func workerRunE(cmd *cobra.Command, args []string) (err error) {
	log.Info("Running worker...")
	ctx, cancel := context.WithCancel(context.Background())
	if verbose, err := cmd.PersistentFlags().GetBool("verbose"); verbose {
		if err != nil {
			log.For("Worker", "Start").Error(err)
			return err
		}

		sarama.Logger = log.Standard()
	}

	kafkaCfg := kafka.Config{
		UserName:  config.KafkaSASLUsername(),
		Password:  config.KafkaSASLPassword(),
		Brokers:   config.KafkaBrokers(),
		Mechanism: config.KafkaSASLMechanism(),
	}

	kafkaClient, err := kafka.NewClient(kafkaCfg)
	if err != nil {
		log.For("Worker", "Start").Error(err)
		return err
	}

	log.Info("Kafka client initiated")
	var workers []*worker.Worker
	notif := make(chan handler.QuotaNotification)
	topics := config.KafkaTopics()
	log.Info(topics)
	wg := &sync.WaitGroup{}
	if len(topics) > 0 {
		kafkaConsumerGroup, err := sarama.NewConsumerGroupFromClient(config.KafkaGroupID(), kafkaClient)
		if err != nil {
			log.For("Worker", "Start").Error(err)
			return err
		}

		consumer := kafka.NewConsumer("price_promo_worker",
			handler.NewQuotaMessageHandler(wg, notif))
		subscriber := worker.NewWorker("kafka_subscriber_worker")
		subscriber.Add("subscriber", worker.NewSubscriberTask(kafkaConsumerGroup,
			strings.Split(topics, ","), consumer))
		workers = append(workers, subscriber)
	}

	for _, w := range workers {
		wg.Add(1)
		go func(w *worker.Worker) {
			err := w.Start(ctx)
			if err != nil {
				log.For("Worker", "Start").Error(err)
			}
		}(w)
		wg.Done()
	}

	terminate := make(chan bool, 1)
	go handler.SendNotifQuota(ctx, notif, wg, terminate)

	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM)
	s := <-sigCh
	terminate <- true
	cancel()
	log.Printf("Got signal %v, attempting graceful shutdown", s)
	wg.Wait()
	time.Sleep(1 * time.Second) //for logging close consumer
	return err
}

func beforeWorker(cmd *cobra.Command, args []string) error {
	return db.Open(config.DatabaseDSN(), config.DatabaseRODSNs()...)
}

func init() {
	WorkerCmd.PreRunE = beforeWorker
	WorkerCmd.RunE = workerRunE
	WorkerCmd.PersistentFlags().BoolP("verbose", "v", false, "verbose sarama connection log to stderr")
}
