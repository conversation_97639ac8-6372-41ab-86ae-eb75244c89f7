package migrate

import (
	"strconv"

	"github.com/spf13/cobra"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/config"
	"happyfresh.io/product-promotion/db/migration"
)

// Cmd :nodoc:
var Cmd = &cobra.Command{
	Use:   "migrate {up|down|drop}",
	Short: "Migrate database",
	RunE:  migrateRun,
}

func migrateRun(cmd *cobra.Command, args []string) (err error) {
	if len(args) < 1 {
		log.For("CLI", "migrate").Error("migrate {up|down|drop}")
		return cmd.Usage()
	}

	directionMap := map[string]migration.Direction{
		"up":   migration.Up,
		"down": migration.Down,
		"drop": migration.Drop,
	}

	steps, err := cmd.PersistentFlags().GetString("steps")
	if err != nil {
		log.For("CLI", "migrate").Error(err)
		return err
	}

	s, err := strconv.Atoi(steps)
	if err != nil {
		log.For("CLI", "migrate").Error(err)
		return err
	}

	return migration.Run(config.DatabaseDSN(), directionMap[args[0]], s)
}

func init() {
	Cmd.PersistentFlags().StringP("steps", "s", "0", "migration steps")
}
