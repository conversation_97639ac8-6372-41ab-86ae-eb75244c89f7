include:
  - project: "hf/tpd/ppc/gitlab-ci-templates"
    ref: "main"
    file: "catalog/golang_base.yml"

variables:
  AWS_SERVICE_NAME: "CatalogService-Promotion"
  AWS_WORKER_SERVICE_NAME: "Catalog-Promotion-Worker"
  CONTAINER_REGISTRY: "448938462703.dkr.ecr.ap-southeast-1.amazonaws.com"
  DOCKER_HOST: "tcp://docker:2375"
  DOCKER_TLS_CERTDIR: ""
  ECS_REGION: $AWS_DEFAULT_REGION
  REGISTRY_IMAGE: "${CONTAINER_REGISTRY}/catalogservice/promotion"
  REGISTRY_WORKER_IMAGE: "${CONTAINER_REGISTRY}/catalog/promotion-worker"

build-and-push-image:
  rules:
    - when: never

k8s-deploy-staging:
  rules:
    - when: never

# Build and deploy worker
build-and-push-worker-image:
  environment: staging
  variables:
    MAKE_TARGET: "docker-build-worker-ci"
  extends:
    - .ex_build_and_push
    - .ex_staging_rule

deploy-worker-staging:
  stage: staging
  environment: staging
  variables:
    AWS_SERVICE_NAME: $AWS_WORKER_SERVICE_NAME
    REGISTRY_IMAGE: $REGISTRY_WORKER_IMAGE
  extends:
    - .ex_deploy_to_ecs
    - .ex_staging_rule
  needs:
    - build-and-push-worker-image

build-and-push-worker-image-production:
  environment: production
  variables:
    MAKE_TARGET: "docker-build-worker-ci"
  extends:
    - .ex_build_and_push
    - .ex_production_rule

deploy-worker-production:
  stage: production
  environment: production
  variables:
    AWS_SERVICE_NAME: $AWS_WORKER_SERVICE_NAME
    REGISTRY_IMAGE: $REGISTRY_WORKER_IMAGE
  extends:
    - .ex_deploy_to_ecs
    - .ex_production_rule
  needs:
    - build-and-push-worker-image-production
