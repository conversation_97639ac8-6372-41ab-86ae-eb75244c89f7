package api

import (
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"happyfresh.io/lib/tz"
	"happyfresh.io/product-promotion/db"
	er "happyfresh.io/product-promotion/lib/errors"
	"happyfresh.io/product-promotion/lib/rpc"
)

func currentDate(countryCode string) (date sql.NullTime, err error) {
	time, err := tz.NowWithTimezone(countryCode)
	if err != nil {
		err = errors.WithStack(&er.ArgError{
			Arg: err.Error(),
		})
		return
	}
	err = date.Scan(time.UTC())
	if err != nil {
		err = errors.WithStack(&er.ArgError{
			Arg: fmt.Sprint("Scan Time Error"),
		})
		return
	}
	return
}

func buildPromotionQueryParam(r *rpc.StockItemPromotionMultiRequest) (*db.PromotionQueryParam, error) {
	if r.Channel == "" {
		return nil, errors.WithStack(&er.ArgError{
			Arg: r.Channel,
		})
	}

	date, err := currentDate(r.CountryCode)
	if err != nil {
		return nil, err
	}

	return &db.PromotionQueryParam{
		ProductCodes: r.ProductCodes,
		StoreIDs:     r.StoreIds,
		ChannelName:  r.Channel,
		Date:         date,
	}, nil
}

func buildPromotionChannelQueryParam(r *rpc.StockItemPromotionMultiRequest) (*db.PromotionQueryParam, error) {
	date, err := currentDate(r.CountryCode)
	if err != nil {
		return nil, err
	}

	return &db.PromotionQueryParam{
		ProductCodes: r.ProductCodes,
		StoreIDs:     r.StoreIds,
		Date:         date,
	}, nil
}

func buildExclusivePromotionParam(r *rpc.ExclusivePromoRequest) (*db.ExclusivePromotionQueryParam, error) {
	countryCode := r.CountryCode
	if len(r.CountryCode) == 0 {
		countryCode = "ID"
	}

	date, err := currentDate(countryCode)
	if err != nil {
		return nil, err
	}

	// Hardcoded promotion_sources id, might be added or changed later
	sources := []int64{11}

	return &db.ExclusivePromotionQueryParam{
		StoreID:   r.StoreId,
		Channel:   r.Channel,
		Date:      date,
		SourceIDs: sources,
	}, nil
}
