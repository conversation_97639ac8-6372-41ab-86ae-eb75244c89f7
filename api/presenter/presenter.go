package presenter

import (
	"happyfresh.io/product-promotion/db"
	"happyfresh.io/product-promotion/lib/rpc"
)

func MapPromotionsToStockItemPromotionsResponse(promotions []*db.PromotionQueryResult) (*rpc.StockItemPromotionMultiResponse, error) {
	result := &rpc.StockItemPromotionMultiResponse{}
	for _, promotion := range promotions {
		if promotion.Active.Bool && promotion.Quota.Valid && promotion.Quota.Int32 > 0 {
			data, _ := MapPromotionToStockItemPromotionResponse(promotion)
			result.StockItemPromotions = append(result.StockItemPromotions, data)
		} else if !promotion.Active.Valid && !promotion.Quota.Valid {
			data, _ := MapPromotionToStockItemPromotionResponse(promotion)
			result.StockItemPromotions = append(result.StockItemPromotions, data)
		}
	}

	return result, nil
}

func MapPromotionToStockItemPromotionResponse(promotion *db.PromotionQueryResult) (*rpc.StockItemPromotionResponse, error) {
	rules := make(map[string]string)
	for k, v := range promotion.Rules.Map {
		rules[k] = v.String
	}

	actions := make(map[string]string)
	for k, v := range promotion.Actions.Map {
		actions[k] = v.String
	}

	return &rpc.StockItemPromotionResponse{
		PromotionCode: promotion.PromotionCode,
		ProductCode:   promotion.ProductCode,
		StoreId:       int64(promotion.StoreID),
		Type:          promotion.Type,
		Channel:       promotion.Channel,
		Rules:         rules,
		Actions:       actions,
		StartDate:     promotion.StartDate,
		LastDate:      promotion.LastDate,
		SourceId:      int64(promotion.SourceID),
	}, nil
}

func MapPromotionDetailInfoToPromotionDetailInfoResponse(promotionDetailInfo *db.PromotionDetailInfo) *rpc.PromotionDetailInfoWithFilterResponse {
	rules := make(map[string]string)
	for k, v := range promotionDetailInfo.Rules.Map {
		rules[k] = v.String
	}

	actions := make(map[string]string)
	for k, v := range promotionDetailInfo.Actions.Map {
		actions[k] = v.String
	}

	return &rpc.PromotionDetailInfoWithFilterResponse{
		PromotionCode:             promotionDetailInfo.PromotionCode,
		ProductCode:               promotionDetailInfo.ProductCode,
		StoreId:                   int64(promotionDetailInfo.StoreID),
		Rules:                     rules,
		Actions:                   actions,
		Channel:                   promotionDetailInfo.Channel,
		Source:                    promotionDetailInfo.Source,
		StartDate:                 promotionDetailInfo.StartDate,
		LastDate:                  promotionDetailInfo.LastDate,
		CreatedAt:                 promotionDetailInfo.CreatedAt,
		UpdatedAt:                 promotionDetailInfo.UpdatedAt,
		Status:                    promotionDetailInfo.Status,
		PromotionsDeletedAt:       promotionDetailInfo.PromotionsDeletedAt,
		PromotionDetailsDeletedAt: promotionDetailInfo.PromotionDetailsDeletedAt,
	}
}

func MapPromotionDetailInfoToStockItemPromotionResponse(promotionDetailInfo *db.PromotionDetailInfo) *rpc.StockItemPromotionResponse {
	rules := make(map[string]string)
	for k, v := range promotionDetailInfo.Rules.Map {
		rules[k] = v.String
	}

	actions := make(map[string]string)
	for k, v := range promotionDetailInfo.Actions.Map {
		actions[k] = v.String
	}

	return &rpc.StockItemPromotionResponse{
		PromotionCode: promotionDetailInfo.PromotionCode,
		StartDate:     promotionDetailInfo.StartDate,
		LastDate:      promotionDetailInfo.LastDate,
		ProductCode:   promotionDetailInfo.ProductCode,
		StoreId:       int64(promotionDetailInfo.StoreID),
		Type:          promotionDetailInfo.Type,
		Channel:       promotionDetailInfo.Channel,
		Rules:         rules,
		Actions:       actions,
		DeletedAt:     promotionDetailInfo.PromotionsDeletedAt,
		SourceId:      int64(promotionDetailInfo.SourceID),
	}
}

func MapPromotionToStockItemAllChannelPromotionResponse(promotion *db.PromotionQueryResult) (*rpc.StockItemPriceAllChannelAndPromotion, error) {
	var promotions []*rpc.PromotionAllChannel
	rules := make(map[string]string)
	for k, v := range promotion.Rules.Map {
		rules[k] = v.String
	}

	actions := make(map[string]string)
	for k, v := range promotion.Actions.Map {
		actions[k] = v.String
	}

	p := &rpc.PromotionAllChannel{
		Code:    promotion.PromotionCode,
		Channel: promotion.Channel,
		Rules:   rules,
		Actions: actions,
	}
	promotions = append(promotions, p)
	return &rpc.StockItemPriceAllChannelAndPromotion{
		ProductCode: promotion.ProductCode,
		StoreId:     int64(promotion.StoreID),
		Promotions:  promotions,
	}, nil
}
