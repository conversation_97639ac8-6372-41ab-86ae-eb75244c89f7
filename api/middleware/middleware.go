package middleware

import (
	"context"

	"google.golang.org/grpc/codes"

	"google.golang.org/grpc/status"

	"github.com/pkg/errors"
	"google.golang.org/grpc"

	"happyfresh.io/lib/log"
	er "happyfresh.io/product-promotion/lib/errors"
)

func GRPCError(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	r, err := handler(ctx, req)
	if err == nil {
		return r, err
	}

	switch errors.Cause(err).(type) {
	case *er.ArgError:
		return r, status.Error(codes.InvalidArgument, err.Error())
	default:
		return r, status.Error(codes.Internal, err.Error())
	}
}

func LogError(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	r, err := handler(ctx, req)
	if err == nil {
		return r, err
	}

	log.Error(err)
	return r, err
}
