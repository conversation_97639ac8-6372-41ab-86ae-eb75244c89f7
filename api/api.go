package api

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	health "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/status"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/api/presenter"
	"happyfresh.io/product-promotion/db"
	"happyfresh.io/product-promotion/lib/rpc"
)

//go:generate protoc --proto_path=${PWD}/../lib/rpc/ --gofast_out=plugins=grpc:${PWD}/../lib/rpc/ ${PWD}/../lib/rpc/promotion.proto

type API struct {
}

// gRPC Check method
// Params: HealthCheckRequest
// Response: HealthCheckResponse
func (a *API) Check(ctx context.Context, in *health.HealthCheckRequest) (*health.HealthCheckResponse, error) {
	log.For("API", "Check").Debugf("Received Check request: %v", in)
	return &health.HealthCheckResponse{Status: health.HealthCheckResponse_SERVING}, nil
}

// gRPC Watch method
// Params: HealthCheckRequest
// Response: Health_WatchServer
func (a *API) Watch(in *health.HealthCheckRequest, _ health.Health_WatchServer) error {
	log.For("API", "Check").Debugf("Received Watch request: %v", in)
	return status.Error(codes.Unimplemented, "Unimplemented")
}

func (a *API) GetAllStockItemPromotion(c context.Context, r *rpc.StockItemPromotionMultiRequest) (*rpc.StockItemPromotionMultiResponse, error) {
	param, err := buildPromotionQueryParam(r)
	if err != nil {
		return nil, err
	}

	promotions, err := db.NewPromotionTable().FindAllActive(param)
	if err != nil {
		return nil, err
	}

	result, err := presenter.MapPromotionsToStockItemPromotionsResponse(promotions)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (a *API) GetAllStockItemPromotionChannel(c context.Context, r *rpc.StockItemPromotionMultiRequest) (*rpc.StockItemPromotionMultiResponse, error) {
	param, err := buildPromotionChannelQueryParam(r)
	if err != nil {
		return nil, err
	}

	promotions, err := db.NewPromotionTable().FindAllActiveChannel(param)
	if err != nil {
		return nil, err
	}

	result, err := presenter.MapPromotionsToStockItemPromotionsResponse(promotions)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (a *API) GetPromotionDetailInfoWithFilter(c context.Context, r *rpc.PromotionDetailInfoWithFilterRequest) (*rpc.PromotionDetailInfoWithFilterMultiResponse, error) {
	if r.Size_ < 1 {
		r.Size_ = 10
	}

	if r.From < 0 {
		r.From = 0
	}

	promotions, count, err := db.NewPromotionTable().FindAllByFilters(r.StoreIds, r.PromotionCode, r.ProductCode, r.StartDate, r.LastDate, r.SourceId, r.Size_, r.From)
	if err != nil {
		return nil, err
	}

	promotionDetailInfo := make([]*rpc.PromotionDetailInfoWithFilterResponse, len(promotions))
	for i := range promotions {
		promotionDetailInfo[i] = presenter.MapPromotionDetailInfoToPromotionDetailInfoResponse(promotions[i])
	}
	return &rpc.PromotionDetailInfoWithFilterMultiResponse{
		PromotionDetailInfo: promotionDetailInfo,
		Count:               int64(count),
	}, nil
}

func (a *API) FindAllStartingPromotions(c context.Context, r *rpc.DatesAndStoreIDs) (*rpc.StockItemPromotionMultiResponse, error) {
	promotions, err := a.getAllStartingPromotions(r.GetDates(), r.GetStoreIds())
	if err != nil {
		return nil, err
	}

	result, err := presenter.MapPromotionsToStockItemPromotionsResponse(promotions)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (a *API) StreamAllStartingPromotions(r *rpc.DatesAndStoreIDs, stream rpc.Api_StreamAllStartingPromotionsServer) error {
	promotions, err := a.getAllStartingPromotions(r.GetDates(), r.GetStoreIds())
	if err != nil {
		return err
	}

	for _, promotion := range promotions {
		p, err := presenter.MapPromotionToStockItemPromotionResponse(promotion)
		if err != nil {
			continue
		}

		if err = stream.Send(p); err != nil {
			continue
		}
	}

	return nil
}

func (a *API) GetPromotedProductCodes(ctx context.Context, r *rpc.StoreIDDateChannel) (*rpc.ProductCodes, error) {
	if len(r.Date) <= 0 || r.StoreId <= 0 || len(r.Channel) <= 0 {
		return nil, errors.New("Bad request")
	}

	productCodes, err := db.NewPromotionTable().GetPromotedProductCodes(int(r.StoreId), r.Date, r.Channel)
	if err != nil {
		return nil, err
	}

	return &rpc.ProductCodes{ProductCodes: productCodes}, nil
}

func (a *API) FindAllEndingPromotions(c context.Context, r *rpc.DatesAndStoreIDs) (*rpc.StockItemPromotionMultiResponse, error) {
	promotions, err := a.getAllEndingPromotions(r.GetDates(), r.GetStoreIds())
	if err != nil {
		return nil, err
	}

	result, err := presenter.MapPromotionsToStockItemPromotionsResponse(promotions)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (a *API) StreamAllEndingPromotions(r *rpc.DatesAndStoreIDs, stream rpc.Api_StreamAllEndingPromotionsServer) error {
	promotions, err := a.getAllEndingPromotions(r.GetDates(), r.GetStoreIds())
	if err != nil {
		return err
	}

	for _, promotion := range promotions {
		p, err := presenter.MapPromotionToStockItemPromotionResponse(promotion)
		if err != nil {
			continue
		}

		if err = stream.Send(p); err != nil {
			continue
		}
	}

	return nil
}

func (a *API) ImportPromotion(c context.Context, r *rpc.ImportPromotionMultiRequest) (*rpc.ImportPromotionResponse, error) {
	promotions := make([]*db.Promotion, len(r.ImportPromotionRequest))
	for i, promo := range r.ImportPromotionRequest {
		promotions[i] = &db.Promotion{
			PromotionBase: &db.PromotionBase{
				PromotionCode: promo.PromotionCode,
				SourceID:      int(promo.SourceId),
				StartDate:     promo.StartDate,
				LastDate:      promo.LastDate,
			},
		}
		for _, promotionDetail := range promo.PromotionDetails {
			promotions[i].PromotionDetails = append(promotions[i].PromotionDetails,
				&db.PromotionDetail{
					PromotionCode: promo.PromotionCode,
					ProductCode:   promotionDetail.ProductCode,
					StoreID:       int(promotionDetail.StoreId),
					ChannelID:     int(promotionDetail.ChannelId),
					Type:          promotionDetail.Type,
					Rules:         promotionDetail.Rules,
					Actions:       promotionDetail.Actions,
				})
		}
	}

	response := &rpc.ImportPromotionResponse{}

	importErrors, err := db.NewPromotionTable().SaveAll(promotions)
	if len(importErrors) > 0 {
		for _, e := range importErrors {
			response.ImportPromotionError = append(response.ImportPromotionError,
				&rpc.ImportPromotionError{
					PromotionCode: e.PromotionCode,
					ProductCode:   e.ProductCode,
					StoreId:       int64(e.StoreID),
					ChannelId:     int64(e.ChannelID),
					Errors:        e.ErrorMessages,
				})
		}

		return response, nil
	}
	if err != nil {
		return response, err
	}

	return response, nil
}

func (a *API) StreamImportPromotion(promotionStream rpc.Api_StreamImportPromotionServer) error {
	var err error
	promotionMap := make(map[string]*db.Promotion)
	for {
		promotionDetail, err := promotionStream.Recv()
		if err != nil {
			break
		}

		promotion, ok := promotionMap[promotionDetail.GetPromotionCode()]
		if !ok {
			pBase := &db.PromotionBase{
				PromotionCode: promotionDetail.GetPromotionCode(),
				SourceID:      int(promotionDetail.GetSourceId()),
				StartDate:     promotionDetail.GetStartDate(),
				LastDate:      promotionDetail.GetLastDate(),
			}
			promotion = &db.Promotion{
				PromotionBase:    pBase,
				PromotionDetails: make([]*db.PromotionDetail, 0),
			}
			promotionMap[promotionDetail.GetPromotionCode()] = promotion
		}

		promotion.PromotionDetails = append(promotion.PromotionDetails, &db.PromotionDetail{
			PromotionCode: promotionDetail.PromotionCode,
			ProductCode:   promotionDetail.ProductCode,
			StoreID:       int(promotionDetail.StoreId),
			ChannelID:     int(promotionDetail.ChannelId),
			Type:          promotionDetail.Type,
			Rules:         promotionDetail.Rules,
			Actions:       promotionDetail.Actions,
		})
	}
	if err != nil && err != io.EOF {
		return err
	}

	promotions := make([]*db.Promotion, 0)
	for _, v := range promotionMap {
		promotions = append(promotions, v)
	}

	response := &rpc.ImportPromotionResponse{}

	importErrors, err := db.NewPromotionTable().SaveAll(promotions)
	if len(importErrors) > 0 {
		for _, e := range importErrors {
			response.ImportPromotionError = append(response.ImportPromotionError,
				&rpc.ImportPromotionError{
					PromotionCode: e.PromotionCode,
					ProductCode:   e.ProductCode,
					StoreId:       int64(e.StoreID),
					ChannelId:     int64(e.ChannelID),
					Errors:        e.ErrorMessages,
				})
		}

		return promotionStream.SendAndClose(response)
	}
	if err != nil {
		return err
	}

	return promotionStream.SendAndClose(response)
}

func (a *API) GetPromotionChannel(c context.Context, r *rpc.PromotionChannelRequest) (*rpc.PromotionChannelResponse, error) {
	channelMap := make(map[string]int64)
	pc, err := db.NewPromotionChannelTable().GetAllPromotionChannel()
	if err != nil {
		return nil, err
	}
	for _, row := range pc {
		channelMap[row.Name] = int64(row.ID)
	}
	res := &rpc.PromotionChannelResponse{
		PromotionChannelInfo: channelMap,
	}
	return res, nil
}

func (a *API) GetPromotionSource(c context.Context, r *rpc.PromotionSourceRequest) (*rpc.PromotionSourceResponse, error) {
	var res []*rpc.PromotionSource
	ps, err := db.NewPromotionSource().GetAllPromotionSource()
	if err != nil {
		log.For("API", "get promotion source").Error(err)
		return nil, fmt.Errorf("GetPromotionSource: failed to get promotion sources")
	}

	for _, row := range ps {
		res = append(res, &rpc.PromotionSource{
			Id:   row.ID,
			Name: row.Name,
		})
	}

	return &rpc.PromotionSourceResponse{PromotionSource: res}, nil
}

func (a *API) DeletePromotions(c context.Context, r *rpc.PromotionCodes) (*rpc.OK, error) {
	deletedIDs, err := db.NewPromotionTable().DeleteAll(r.PromotionCodes)
	return &rpc.OK{Ok: err == nil && len(deletedIDs) > 0}, err
}

func (a *API) DeletePromotionBasedOnSKU(stream rpc.Api_DeletePromotionBasedOnSKUServer) error {
	var (
		err         error
		errMessages []string
	)

	for {
		pd, err := stream.Recv()
		if err != nil {
			break
		}

		deletedIDs, err := db.NewPromotionTable().DeletePromoBasedOnSKU(pd.ProductCode, pd.StoreIds, pd.PromotionCodes, pd.Channels)
		if err != nil {
			break
		}

		if len(deletedIDs) == 0 {
			errMsg := fmt.Sprintf("%v,%s,%v,%v", pd.PromotionCodes, pd.ProductCode, pd.StoreIds, pd.Channels)
			errMessages = append(errMessages, errMsg)
		}
	}

	if err != nil && err != io.EOF {
		log.For("API", "delete promotion based on SKU").Error(err)
		return err
	}

	return stream.SendAndClose(&rpc.DeletePromotionDetailsResponse{
		Ok:     err == nil,
		Errors: errMessages,
	})
}

func (a *API) GetPromotionDetailByPromotionCode(r *rpc.PromotionDetailByPromotionCodeRequest, stream rpc.Api_GetPromotionDetailByPromotionCodeServer) error {
	promotions, err := db.NewPromotionTable().FindPromotionDetailsByPromoCode(r.PromotionCode)
	if err != nil {
		return err
	}

	for _, promotion := range promotions {
		p := presenter.MapPromotionDetailInfoToStockItemPromotionResponse(promotion)

		if err = stream.Send(p); err != nil {
			continue
		}
	}

	return nil
}

func (a *API) StreamActivePromotedStockItems(r *rpc.PromotedStockItemRequest, stream rpc.Api_StreamActivePromotedStockItemsServer) error {
	promotions, err := a.getAllStartingPromotions(r.GetDates(), r.GetStoreIds())
	if err != nil {
		return err
	}

	for _, promotion := range promotions {
		p, err := presenter.MapPromotionToStockItemAllChannelPromotionResponse(promotion)
		if err != nil {
			continue
		}

		if err = stream.Send(p); err != nil {
			continue
		}
	}

	return nil
}
func (a *API) StreamInactivePromotedStockItems(r *rpc.PromotedStockItemRequest, stream rpc.Api_StreamInactivePromotedStockItemsServer) error {
	promotions, err := a.getAllEndingPromotions(r.GetDates(), r.GetStoreIds())
	if err != nil {
		return err
	}

	for _, promotion := range promotions {
		p, err := presenter.MapPromotionToStockItemAllChannelPromotionResponse(promotion)
		if err != nil {
			continue
		}

		if err = stream.Send(p); err != nil {
			continue
		}
	}

	return nil
}

func (a *API) Ping(c context.Context, r *rpc.Null) (*rpc.OK, error) {
	return &rpc.OK{Ok: true}, nil
}

func (a *API) getAllStartingPromotions(dates []string, storeIDs []int64) ([]*db.PromotionQueryResult, error) {
	if len(dates) <= 0 {
		return nil, errors.New("Dates cannot be empty")
	}

	if len(storeIDs) <= 0 {
		return nil, errors.New("Store IDs cannot be empty")
	}

	for _, date := range dates {
		_, err := time.Parse("2006/01/02", date)
		if err != nil {
			return nil, err
		}
	}

	return db.NewPromotionTable().FindAllStarting(dates, storeIDs)
}

func (a *API) getAllEndingPromotions(dates []string, storeIDs []int64) ([]*db.PromotionQueryResult, error) {
	if len(dates) <= 0 {
		return nil, errors.New("Dates cannot be empty")
	}

	if len(storeIDs) <= 0 {
		return nil, errors.New("Store IDs cannot be empty")
	}

	for _, date := range dates {
		_, err := time.Parse("2006/01/02", date)
		if err != nil {
			return nil, err
		}
	}

	return db.NewPromotionTable().FindAllEnding(dates, storeIDs)
}

func (a *API) ImportPromotionQuota(ctx context.Context, req *rpc.ImportPromotionQuotaRequestMultiple) (*rpc.ImportPromotionQuotaResponse, error) {
	var err error
	var errorFlag bool
	var productQuotas []db.PromotionQuota
	var promotionCode []string
	responses := &rpc.ImportPromotionQuotaResponse{}
	for _, reqQuota := range req.ImportPromotionQuotaRequest {
		var data db.PromotionQuota
		if strings.TrimSpace(reqQuota.ProductCode) == "" || strings.TrimSpace(reqQuota.PromotionCode) == "" {
			response := &rpc.ImportPromotionQuotaError{}
			response.ProductCode = reqQuota.ProductCode
			response.PromotionCode = reqQuota.PromotionCode
			response.Errors = append(response.Errors, fmt.Sprintf("Product Code must Be Set and Promotion Code must Be Set "))
			responses.ImportPromotionQuotaError = append(responses.ImportPromotionQuotaError, response)
			errorFlag = true
		}

		if !errorFlag {
			data.Active = reqQuota.Active
			data.ProductCode = reqQuota.ProductCode
			data.PromotionCode = reqQuota.PromotionCode
			data.Quota = int(reqQuota.Quota)
			productQuotas = append(productQuotas, data)
			promotionCode = append(promotionCode, reqQuota.PromotionCode)
		}
	}

	dataPromo, err := db.NewPromotionTable().FindIdAndPromoCodeByPromoCode(promotionCode)
	if err != nil {
		return responses, err
	}

	errormsg, err := db.NewPromotionQuota(nil).InsertAllData(&productQuotas, dataPromo)
	if len(errormsg) > 0 {
		for _, data := range errormsg {
			response := &rpc.ImportPromotionQuotaError{}
			response.ProductCode = data.ProductCode
			response.PromotionCode = data.PromotionCode
			response.Errors = append(response.Errors, data.ErrorMessages)
			responses.ImportPromotionQuotaError = append(responses.ImportPromotionQuotaError, response)
		}
		return responses, err
	}

	return responses, err
}

func (a *API) StreamImportPromotionQuota(stream rpc.Api_StreamImportPromotionQuotaServer) error {
	var err error
	var productQuotas []db.PromotionQuota
	var promotionCode []string
	responses := &rpc.ImportPromotionQuotaResponse{}
	for {
		var errorFlag bool
		var data db.PromotionQuota
		pd, err := stream.Recv()
		if err == io.EOF {
			break
		} else if err != nil {
			log.For("API", "stream import promotion quota").Error(err)
			break
		}

		if strings.TrimSpace(pd.ProductCode) == "" || strings.TrimSpace(pd.PromotionCode) == "" {
			response := &rpc.ImportPromotionQuotaError{}
			response.ProductCode = pd.ProductCode
			response.PromotionCode = pd.PromotionCode
			response.Errors = append(response.Errors, fmt.Sprintf("Product Code must Be Set and Promotion Code must Be Set "))
			responses.ImportPromotionQuotaError = append(responses.ImportPromotionQuotaError, response)
			errorFlag = true
		}

		if !errorFlag {
			data.Active = pd.Active
			data.ProductCode = pd.ProductCode
			data.PromotionCode = pd.PromotionCode
			data.Quota = int(pd.Quota)
			productQuotas = append(productQuotas, data)
			promotionCode = append(promotionCode, pd.PromotionCode)
		}
	}

	if len(productQuotas) == 0 {
		stream.SendAndClose(responses)
		return err
	}

	dataPromo, err := db.NewPromotionTable().FindIdAndPromoCodeByPromoCode(promotionCode)
	if err != nil {
		stream.SendAndClose(responses)
		return err
	}

	errormsg, err := db.NewPromotionQuota(nil).InsertAllData(&productQuotas, dataPromo)
	if len(errormsg) > 0 {
		for _, data := range errormsg {
			response := &rpc.ImportPromotionQuotaError{}
			response.ProductCode = data.ProductCode
			response.PromotionCode = data.PromotionCode
			response.Errors = append(response.Errors, data.ErrorMessages)
			responses.ImportPromotionQuotaError = append(responses.ImportPromotionQuotaError, response)
		}
		stream.SendAndClose(responses)
		return err
	}

	stream.SendAndClose(responses)
	return err
}

func (a *API) GetPromotionQuotaDetailInfoWithFilter(ctx context.Context, req *rpc.ImportPromotionQuotaRequest) (*rpc.PromotionQuotaDetailInfoWithFilterMultiResponse, error) {
	result := &rpc.PromotionQuotaDetailInfoWithFilterMultiResponse{}
	resultPromotionFilter, err := db.NewPromotionQuota(nil).GetQuotaByFilter(&req.PromotionCode, &req.ProductCode, &req.Quota, &req.Active)
	if err != nil {
		return &rpc.PromotionQuotaDetailInfoWithFilterMultiResponse{}, err
	}

	for _, data := range resultPromotionFilter {
		resultData := &rpc.PromotionQuotaDetailInfoWithFilterResponse{}
		resultData.Active = data.Active
		resultData.ProductCode = data.ProductCode
		resultData.PromotionCode = data.PromotionCode
		resultData.Quota = int32(data.Quota)
		resultData.CreatedAt = data.CreatedAt.Time.UTC().Format("2006-01-02")
		resultData.UpdatedAt = data.UpdatedAt.Time.UTC().Format("2006-01-02")
		result.PromotionQuotaDetailInfo = append(result.PromotionQuotaDetailInfo, resultData)
		result.Count = result.Count + 1
	}
	return result, err
}

func (a *API) GetExclusivePromotion(c context.Context, r *rpc.ExclusivePromoRequest) (*rpc.ProductCodes, error) {
	param, err := buildExclusivePromotionParam(r)
	if err != nil {
		return nil, err
	}

	promotions, err := db.NewPromotionTable().GetExclusivePromoProductCode(param)
	if err != nil {
		return nil, err
	}

	result := &rpc.ProductCodes{}
	result.ProductCodes = append(result.ProductCodes, promotions...)

	return result, nil
}
