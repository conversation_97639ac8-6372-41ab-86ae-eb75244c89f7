package db

import (
	"database/sql"

	"happyfresh.io/lib/log"
)

const (
	channelAssetTag = "resources/sql/2.promotion_channel.sql"
)

type PromotionChannel struct {
	ID   int
	Name string
}

type PromotionChannelTable struct {
}

func NewPromotionChannelTable() *PromotionChannelTable {
	return &PromotionChannelTable{}
}

func (c *PromotionChannelTable) Save(channel *PromotionChannel) error {
	queries := loadQueries(channelAssetTag)
	err := tx(func(db *sql.Tx) error {
		query, err := db.Prepare(queries["create-channel-with-id"])
		if err != nil {
			log.For("db", "ChannelTable#Save").Error(err.Error())
			return err
		}

		_, err = query.Exec(channel.ID, channel.Name)
		if err != nil {
			log.For("db", "ChannelTable#Save").Error(err.Error())
			return err
		}

		err = query.Close()
		if err != nil {
			log.For("db", "ChannelTable#Save").Error(err.Error())
			return err
		}

		return nil
	})
	if err != nil {
		log.For("db", "ChannelTable#Save").Error(err.Error())
		return err
	}

	return nil
}

func (c *PromotionChannelTable) GetAllPromotionChannel() ([]*PromotionChannel, error) {
	queries := loadQueries(channelAssetTag)
	r, err := ro().Query(queries["get-all-channel"])
	if err != nil {
		log.For("db", "ChannelTable#GetAllPromotionChannel")
	}
	defer r.Close()

	var pc []*PromotionChannel
	for r.Next() {
		p := &PromotionChannel{}
		err := r.Scan(&p.ID, &p.Name)
		if err != nil {
			log.For("db", "ChannelTable#GetAllPromotionChannel")
		}
		pc = append(pc, p)
	}

	return pc, nil
}
