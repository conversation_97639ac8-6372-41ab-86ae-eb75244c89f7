package db

import (
	"database/sql"

	"happyfresh.io/lib/log"
)

const (
	promotionSourceAssetTag = "resources/sql/3.promotion_source.sql"
)

type PromotionSource struct {
	ID   int64
	Name string
}

type PromotionSourceTable struct {
}

func NewPromotionSource() *PromotionSource {
	return &PromotionSource{}
}

func NewPromotionSourceTable() *PromotionSourceTable {
	return &PromotionSourceTable{}
}

func (p *PromotionSource) GetAllPromotionSource() (ps []*PromotionSource, err error) {
	queries := loadQueries(promotionSourceAssetTag)
	r, err := ro().Query(queries["get-all-promotion-source"])
	if err != nil {
		log.For("db", "GetAllPromotionSource").Error(err)
		return nil, err
	}

	defer r.Close()

	for r.Next() {
		p := &PromotionSource{}
		err := r.<PERSON>an(&p.ID, &p.Name)
		if err != nil {
			log.For("db", "GetAllPromotionSource").Error(err.Error())
			continue
		}
		ps = append(ps, p)
	}
	return ps, nil
}

func (c *PromotionSourceTable) Save(source *PromotionSource) error {
	queries := loadQueries(promotionSourceAssetTag)
	err := tx(func(db *sql.Tx) error {
		query, err := db.Prepare(queries["create-source-with-id"])
		if err != nil {
			log.For("db", "PromotionSourceTable#Save").Error(err.Error())
			return err
		}

		_, err = query.Exec(source.ID, source.Name)
		if err != nil {
			log.For("db", "PromotionSourceTable#Save").Error(err.Error())
			return err
		}

		err = query.Close()
		if err != nil {
			log.For("db", "PromotionSourceTable#Save").Error(err.Error())
			return err
		}

		return nil
	})
	if err != nil {
		log.For("db", "PromotionSourceTable#Save").Error(err.Error())
		return err
	}

	return nil
}
