package db

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/lib/pq/hstore"
	"happyfresh.io/lib/log"
)

const (
	promotionAssetTag = "resources/sql/1.promotion.sql"
)

type Promotion struct {
	*PromotionBase
	PromotionDetails []*PromotionDetail
}

type PromotionBase struct {
	ID            int64
	PromotionCode string
	Source        string
	SourceID      int
	StartDate     string
	LastDate      string
	CreatedAt     string
	UpdatedAt     string
	DeletedAt     string
}

type PromotionDetail struct {
	PromotionCode string
	ProductCode   string
	StoreID       int
	ChannelID     int
	Type          string
	Rules         map[string]string
	Actions       map[string]string
	DeletedAt     string
}

type PromotionDetailError struct {
	*PromotionDetail
	Errors        []error
	ErrorMessages []string
}

type PromotionQueryParam struct {
	ProductCodes []string
	StoreIDs     []string
	ChannelName  string
	Date         sql.NullTime
}

type ExclusivePromotionQueryParam struct {
	StoreID   int64
	Channel   string
	Date      sql.NullTime
	SourceIDs []int64
}

type PromotionQueryResult struct {
	PromotionCode string
	StartDate     string
	LastDate      string
	ProductCode   string
	StoreID       int
	Type          string
	Channel       string
	Rules         hstore.Hstore
	Actions       hstore.Hstore
	SourceID      int
	Quota         sql.NullInt32
	Active        sql.NullBool
}

type PromotionDetailInfo struct {
	PromotionCode             string
	ProductCode               string
	StoreID                   int
	Rules                     hstore.Hstore
	Actions                   hstore.Hstore
	Channel                   string
	Source                    string
	StartDate                 string
	LastDate                  string
	CreatedAt                 string
	UpdatedAt                 string
	Status                    string
	PromotionsDeletedAt       string
	PromotionDetailsDeletedAt string
	Type                      string
	SourceID                  int
}

type PromotionTable struct {
}

func NewPromotionTable() *PromotionTable {
	return &PromotionTable{}
}

func (p *PromotionTable) FindAllActive(param *PromotionQueryParam) ([]*PromotionQueryResult, error) {
	if len(param.StoreIDs) <= 0 || len(param.ProductCodes) <= 0 {
		return make([]*PromotionQueryResult, 0), nil
	}

	queries := loadQueries(promotionAssetTag)
	rows, err := p.findAllByPromotionQueryParam(queries["get-active-promos-by-product-code-store-id-channel-date"], param)
	if err != nil {
		log.For("db", "FindAllActive").Error(err.Error())
		return nil, err
	}

	var result []*PromotionQueryResult
	for rows.Next() {
		r := &PromotionQueryResult{}
		err = rows.Scan(
			&r.PromotionCode,
			&r.StartDate,
			&r.LastDate,
			&r.ProductCode,
			&r.StoreID,
			&r.Type,
			&r.Rules,
			&r.Actions,
			&r.SourceID,
			&r.Quota,
			&r.Active,
		)
		if err != nil {
			log.For("db", "FindAllActive").Error(err.Error())
			return nil, err
		}

		result = append(result, r)
	}

	return result, nil
}

func (p *PromotionTable) FindAllActiveChannel(param *PromotionQueryParam) ([]*PromotionQueryResult, error) {
	if len(param.StoreIDs) <= 0 || len(param.ProductCodes) <= 0 {
		return make([]*PromotionQueryResult, 0), nil
	}

	queries := loadQueries(promotionAssetTag)
	rows, err := p.findAllByPromotionChannelQueryParam(queries["get-active-promos-by-product-code-store-id-date"], param)
	if err != nil {
		log.For("db", "FindAllActiveChannel").Error(err.Error())
		return nil, err
	}

	var result []*PromotionQueryResult
	for rows.Next() {
		r := &PromotionQueryResult{}
		err = rows.Scan(
			&r.PromotionCode,
			&r.StartDate,
			&r.LastDate,
			&r.ProductCode,
			&r.StoreID,
			&r.Channel,
			&r.Type,
			&r.Rules,
			&r.Actions,
			&r.SourceID,
			&r.Quota,
			&r.Active,
		)
		if err != nil {
			log.For("db", "FindAllActiveChannel").Error(err.Error())
			return nil, err
		}

		result = append(result, r)
	}

	return result, nil
}

// FindAllOverlappingPromotion Finds all existing promotions with the given parameter but different promotion code
func (p *PromotionTable) FindAllOverlapping(promotionCode, productCode string, storeID, channelID int, startDate, lastDate string) ([]*PromotionBase, error) {
	query, _ := loadQueries(promotionAssetTag)["get-overlap-promo"]
	r, err := ro().Query(query, promotionCode, storeID, productCode, channelID, startDate, lastDate)
	if err != nil {
		log.For("db", "FindAllOverlapping").Error(err.Error())
		return nil, err
	}

	var overlappingPromos []*PromotionBase
	for r.Next() {
		p := &PromotionBase{}
		r.Scan(
			&p.PromotionCode,
			&p.StartDate,
			&p.LastDate,
		)

		overlappingPromos = append(overlappingPromos, p)
	}
	return overlappingPromos, nil
}

func (p *PromotionTable) FindAllByFilters(storeIDs []int64, promotionCode, productCode, startDate, lastDate string, promotionSourceID, size, from int64) ([]*PromotionDetailInfo, int, error) {
	eq := sq.Eq{}
	if len(storeIDs) > 0 {
		eq["pd.store_id"] = storeIDs
	}
	if len(productCode) > 0 {
		eq["pd.product_code"] = productCode
	}
	if promotionSourceID > 0 {
		eq["p.source_id"] = promotionSourceID
	}
	if len(promotionCode) > 0 {
		eq["p.promotion_code"] = promotionCode
	}

	startDateFilter := ""
	if len(startDate) > 0 {
		startDateFilter = fmt.Sprintf("p.new_start_date >= to_timestamp('%s', 'yyyy/MM/dd HH24:MI:SS')", startDate)
	}

	lastDateFilter := ""
	if len(lastDate) > 0 {
		lastDateFilter = fmt.Sprintf("p.new_last_date <= to_timestamp('%s', 'yyyy/MM/dd HH24:MI:SS')", lastDate)
	}

	if size <= 0 {
		size = 10
	}

	psql := sq.StatementBuilder.PlaceholderFormat(sq.Dollar)
	queryBuilder := psql.Select(
		"p.promotion_code",
		"pd.product_code",
		"pd.store_id",
		"pd.actions",
		"pd.rules",
		"c.name",
		"s.name",
		"to_char(p.new_start_date, 'yyyy/MM/dd HH24:MI:SS') AS start_date",
		"to_char(p.new_last_date, 'yyyy/MM/dd HH24:MI:SS') AS last_date",
		"to_char(p.created_at, 'yyyy/MM/dd HH24:MI:SS') AS created_at",
		"to_char(p.updated_at, 'yyyy/MM/dd HH24:MI:SS') AS updated_at",
		"CASE WHEN p.new_start_date <= NOW() AND p.new_last_date >= NOW() AND p.deleted_at IS NULL AND pd.deleted_at IS NULL AND ( apq.quota IS NULL OR apq.quota > 0) AND (apq.active IS NULL OR apq.active IS TRUE) THEN 'ACTIVE' ELSE 'INACTIVE' END",
		"COALESCE(to_char(p.deleted_at, 'yyyy/MM/dd HH24:MI:SS'), '')",
		"COALESCE(to_char(pd.deleted_at, 'yyyy/MM/dd HH24:MI:SS'), '')").
		From("aloha_promotion_details pd").
		JoinClause("INNER JOIN aloha_promotions p ON pd.promotion_id = p.id").
		JoinClause("INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id").
		JoinClause("INNER JOIN aloha_promotion_sources s ON s.id = p.source_id").
		JoinClause("LEFT JOIN aloha_promotion_quota_new apq ON apq.promotion_id = p.id AND apq.product_code = pd.product_code")

	countQueryBuilder := psql.Select(
		"COUNT (pd.id)").
		From("aloha_promotion_details pd").
		JoinClause("INNER JOIN aloha_promotions p ON pd.promotion_id = p.id").
		JoinClause("INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id").
		JoinClause("INNER JOIN aloha_promotion_sources s ON s.id = p.source_id").
		JoinClause("LEFT JOIN aloha_promotion_quota_new apq ON apq.promotion_id = p.id AND apq.product_code = pd.product_code")

	if len(eq) > 0 {
		queryBuilder = queryBuilder.Where(eq)
		countQueryBuilder = countQueryBuilder.Where(eq)
	}

	if len(startDateFilter) > 0 {
		queryBuilder = queryBuilder.Where(startDateFilter)
		countQueryBuilder = countQueryBuilder.Where(startDateFilter)
	}

	if len(lastDateFilter) > 0 {
		queryBuilder = queryBuilder.Where(lastDateFilter)
		countQueryBuilder = countQueryBuilder.Where(lastDateFilter)
	}

	query, args, err := countQueryBuilder.ToSql()
	if err != nil {
		log.For("db", "FindAllByFilters").Error(err)
		return nil, 0, err
	}

	row := ro().QueryRow(query, args...)
	var count int
	err = row.Scan(&count)
	if err != nil {
		log.For("db", "FindAllByFilters").Error(err)
		return nil, 0, err
	}

	query, args, err = queryBuilder.
		OrderBy("p.updated_at DESC").
		Limit(uint64(size)).
		Offset(uint64(from)).
		ToSql()
	if err != nil {
		log.For("db", "FindAllByFilters").Error(err)
		return nil, 0, err
	}

	rows, err := ro().Query(query, args...)
	if err != nil {
		log.For("db", "FindAllByFilters").Error(err)
		return nil, 0, err
	}

	var promotionDetailInfo []*PromotionDetailInfo
	defer rows.Close()
	for rows.Next() {
		p := &PromotionDetailInfo{}
		if err := rows.Scan(
			&p.PromotionCode,
			&p.ProductCode,
			&p.StoreID,
			&p.Actions,
			&p.Rules,
			&p.Channel,
			&p.Source,
			&p.StartDate,
			&p.LastDate,
			&p.CreatedAt,
			&p.UpdatedAt,
			&p.Status,
			&p.PromotionsDeletedAt,
			&p.PromotionDetailsDeletedAt); err != nil {
			log.For("db", "FindAllByFilters").Error(err)
		}

		promotionDetailInfo = append(promotionDetailInfo, p)
	}
	if err != nil {
		log.For("db", "FindAllByFilters").Error(err)
		return nil, 0, err
	}

	return promotionDetailInfo, count, nil
}

func (p *PromotionTable) FindAllStarting(dates []string, storeIDs []int64) ([]*PromotionQueryResult, error) {
	dateSql := sq.Or{}
	for _, date := range dates {
		dateSql = append(dateSql, sq.Expr(fmt.Sprintf("p.new_start_date::DATE = to_date('%s', 'yyyy/MM/dd')", date)))
	}

	andFilter := sq.And{}
	andFilter = append(andFilter, dateSql)
	andFilter = append(andFilter, sq.Expr("p.deleted_at IS NULL"))
	andFilter = append(andFilter, sq.Expr("pd.deleted_at IS NULL"))

	return p.findAllByExactDatesAndStoreIDs(andFilter, storeIDs)
}

func (p *PromotionTable) FindAllOnGoing(dates []string, storeIDs []int64) ([]*PromotionQueryResult, error) {
	onGoingPromotionSql := sq.And{}
	dateSql := sq.Or{}
	for _, date := range dates {
		d := sq.And{}
		d = append(d, sq.Expr(fmt.Sprintf("p.new_start_date <= to_timestamp('%s', 'yyyy/MM/dd HH24:MI:SS')", date)))
		d = append(d, sq.Expr(fmt.Sprintf("p.new_last_date >= to_timestamp('%s', 'yyyy/MM/dd HH24:MI:SS')", date)))
		dateSql = append(dateSql, d)
	}

	onGoingPromotionSql = append(onGoingPromotionSql, dateSql)
	onGoingPromotionSql = append(onGoingPromotionSql, sq.Expr("p.deleted_at IS NULL"))

	return p.findAllByExactDatesAndStoreIDs(onGoingPromotionSql, storeIDs)
}

func (p *PromotionTable) FindAllEnding(dates []string, storeIDs []int64) ([]*PromotionQueryResult, error) {
	dateSql := sq.Or{}
	for _, date := range dates {
		dateSql = append(dateSql, sq.Expr(fmt.Sprintf("p.new_last_date::DATE = to_date('%s', 'yyyy/MM/dd HH24:MI:SS')", date)))
	}
	for _, date := range dates {
		dateSql = append(dateSql, sq.Expr(fmt.Sprintf("p.deleted_at = to_timestamp('%s', 'yyyy/MM/dd HH24:MI:SS')", date)))
		dateSql = append(dateSql, sq.Expr(fmt.Sprintf("pd.deleted_at = to_timestamp('%s', 'yyyy/MM/dd HH24:MI:SS')", date)))
	}

	return p.findAllByExactDatesAndStoreIDs(dateSql, storeIDs)
}

func (p *PromotionTable) GetPromotedProductCodes(storeID int, date, channel string) ([]string, error) {
	if len(date) <= 0 || storeID <= 0 || len(channel) <= 0 {
		return []string{}, nil
	}

	queries := loadQueries(promotionAssetTag)
	rows, err := ro().Query(queries["get-promoted-product-codes-by-date-store-id-channel"], date, storeID, channel)
	if err != nil {
		log.For("db", "GetPromotedProductCodes").Error(err.Error())
		return nil, err
	}

	skus := []string{}
	for rows.Next() {
		var s string
		if err := rows.Scan(&s); err != nil {
			return nil, err
		}

		skus = append(skus, s)
	}

	return skus, nil
}

func (p *PromotionTable) SaveAll(promotions []*Promotion) ([]*PromotionDetailError, error) {
	queries := loadQueries(promotionAssetTag)

	var promoDetailErrors []*PromotionDetailError
	for _, promo := range promotions {
		for _, detail := range promo.PromotionDetails {
			overlappingPromos, err := p.FindAllOverlapping(promo.PromotionCode, detail.ProductCode, detail.StoreID, detail.ChannelID, promo.StartDate, promo.LastDate)
			if err != nil {
				log.For("db", "SaveAll").Error(err.Error())
			}
			if len(overlappingPromos) == 0 {
				continue
			}

			var errs []error
			var errMsgs []string
			for _, overlappingPromo := range overlappingPromos {
				errMsg := fmt.Sprintf("Promotion overlaps with %s at %s until %s", overlappingPromo.PromotionCode, overlappingPromo.StartDate, overlappingPromo.LastDate)

				errMsgs = append(errMsgs, errMsg)
				errs = append(errs, errors.New(errMsg))
			}

			promoDetailErrors = append(promoDetailErrors, &PromotionDetailError{
				PromotionDetail: detail,
				Errors:          errs,
				ErrorMessages:   errMsgs,
			})
		}
	}
	if len(promoDetailErrors) > 0 {
		return promoDetailErrors, errors.New("Overlapping promotions detected")
	}

	err := tx(func(db *sql.Tx) error {
		insertPromotionQuery, err := db.Prepare(queries["create-promotion"])
		if err != nil {
			log.For("db", "Save").Error(err.Error())
			return err
		}
		for _, promotion := range promotions {
			r := insertPromotionQuery.QueryRow(
				promotion.PromotionCode,
				promotion.SourceID,
				promotion.StartDate,
				promotion.LastDate,
			)

			var id int64
			err := r.Scan(&id)
			if err != nil {
				log.For("db", "SaveAll").Error(err)
				return err
			}

			promotion.ID = id
		}
		err = insertPromotionQuery.Close()
		if err != nil {
			log.For("db", "SaveAll").Error(err)
			return err
		}

		return nil
	})
	if err != nil {
		log.For("db", "SaveAll").Error(err)
		return nil, err
	}

	err = tx(func(db *sql.Tx) error {
		insertPromotionDetailQuery, err := db.Prepare(queries["create-promotion-detail"])
		if err != nil {
			log.For("db", "SaveAll").Error(err)
			return err
		}
		for _, promotion := range promotions {
			for _, promotionDetail := range promotion.PromotionDetails {
				rules := hstore.Hstore{
					Map: make(map[string]sql.NullString),
				}
				for k, v := range promotionDetail.Rules {
					rules.Map[k] = sql.NullString{
						String: v,
						Valid:  true,
					}
				}

				actions := hstore.Hstore{
					Map: make(map[string]sql.NullString),
				}
				for k, v := range promotionDetail.Actions {
					actions.Map[k] = sql.NullString{
						String: v,
						Valid:  true,
					}
				}

				_, err = insertPromotionDetailQuery.Exec(
					promotion.ID,
					promotionDetail.ProductCode,
					promotionDetail.StoreID,
					promotionDetail.ChannelID,
					promotionDetail.Type,
					rules,
					actions,
				)
				if err != nil {
					log.For("db", "SaveAll").Error(err.Error())
					return err
				}
			}
		}
		err = insertPromotionDetailQuery.Close()
		if err != nil {
			log.For("db", "SaveAll").Error(err.Error())
			return err
		}

		return nil
	})

	return nil, err
}

// DeleteAll deletes promotions by codes and returning deleted promotion IDs and an error if any
func (p *PromotionTable) DeleteAll(promotionCodes []string) ([]int, error) {
	if len(promotionCodes) <= 0 {
		return make([]int, 0), nil
	}

	query, ok := loadQueries(promotionAssetTag)["delete-promotion-by-codes"]
	if !ok {
		return nil, errors.New("Query not found")
	}

	r, err := db.Query(fmt.Sprintf(query, strings.Join(p.toSQLStrings(promotionCodes), ",")))
	if err != nil {
		log.For("db", "DeleteAll").Error(err)
		return nil, err
	}

	var deletedIDs []int
	for r.Next() {
		var deletedID int
		r.Scan(&deletedID)
		deletedIDs = append(deletedIDs, deletedID)
	}

	return deletedIDs, err
}

func (p *PromotionTable) DeleteAllWithCustomTime(promotionCodes []string, t string) ([]int, error) {
	if len(promotionCodes) <= 0 {
		return make([]int, 0), nil
	}
	if len(t) <= 0 {
		return make([]int, 0), errors.New("Custom time string cannot be null")
	}

	_, err := time.Parse("2006-01-02 15:04:05", t)
	if err != nil {
		return nil, err
	}

	query, ok := loadQueries(promotionAssetTag)["delete-promotion-by-codes-custom-time"]
	if !ok {
		return nil, errors.New("Query not found")
	}

	r, err := db.Query(fmt.Sprintf(query, strings.Join(p.toSQLStrings(promotionCodes), ",")), t)
	if err != nil {
		log.For("db", "DeleteAllWithCustomTime").Error(err)
		return nil, err
	}

	var deletedIDs []int
	for r.Next() {
		var deletedID int
		r.Scan(&deletedID)
		deletedIDs = append(deletedIDs, deletedID)
	}

	return deletedIDs, err
}

// DeletePromoBasedOnSKU :nodoc:
func (p *PromotionTable) DeletePromoBasedOnSKU(productCode string, storeIDs []int64, promotionCodes, channels []string) ([]int, error) {
	var additionalQuery []string

	if len(productCode) <= 0 {
		return make([]int, 0), nil
	}

	query, ok := loadQueries(promotionAssetTag)["delete-promotion-by-sku"]
	if !ok {
		return nil, errors.New("Query not found")
	}

	if len(storeIDs) > 0 {
		storeIDsString := strings.Trim(strings.Join(strings.Fields(fmt.Sprint(storeIDs)), ","), "[]")
		additionalQuery = append(additionalQuery, fmt.Sprintf("AND store_id IN (%s)", storeIDsString))
	}

	if len(promotionCodes) > 0 {
		promotionCodeQuery := fmt.Sprintf("AND promotion_id IN (SELECT p.id FROM aloha_promotions p WHERE p.promotion_code IN (%s))", strings.Join(p.toSQLStrings(promotionCodes), ","))
		additionalQuery = append(additionalQuery, promotionCodeQuery)
	}

	if len(channels) > 0 {
		channelsQuery := fmt.Sprintf("AND channel_id IN (SELECT c.id FROM aloha_promotion_channels c WHERE c.name in (%s))", strings.Join(p.toSQLStrings(channels), ","))
		additionalQuery = append(additionalQuery, channelsQuery)
	}

	additionalQuery = append(additionalQuery, "RETURNING id;")

	r, err := db.Query(fmt.Sprintf(query, strings.Join(additionalQuery, " ")), productCode)
	if err != nil {
		log.For("db", "DeletePromoBasedOnSKU").Error(err)
		return make([]int, 0), err
	}

	var deletedIDs []int
	for r.Next() {
		var deletedID int
		r.Scan(&deletedID)
		deletedIDs = append(deletedIDs, deletedID)
	}

	return deletedIDs, err
}

func (p *PromotionTable) FindPromotionDetailsByPromoCode(promotionCode string) ([]*PromotionDetailInfo, error) {
	if len(promotionCode) <= 0 {
		return nil, nil
	}

	query, ok := loadQueries(promotionAssetTag)["get-promotion-detail-by-promotion-code"]
	if !ok {
		return nil, errors.New("Query not found")
	}

	r, err := db.Query(query, promotionCode)
	if err != nil {
		log.For("db", "FindPromotionDetailsByPromoCode").Error(err)
		return nil, err
	}
	var promotions []*PromotionDetailInfo
	for r.Next() {
		promotion := &PromotionDetailInfo{}
		r.Scan(
			&promotion.PromotionCode,
			&promotion.StartDate,
			&promotion.LastDate,
			&promotion.ProductCode,
			&promotion.StoreID,
			&promotion.Type,
			&promotion.Channel,
			&promotion.Rules,
			&promotion.Actions,
			&promotion.SourceID,
			&promotion.PromotionsDeletedAt,
		)
		promotions = append(promotions, promotion)
	}

	return promotions, err
}

func (p *PromotionTable) findAllByPromotionQueryParam(query string, param *PromotionQueryParam) (*sql.Rows, error) {
	productCodes := strings.Join(p.toSQLStrings(param.ProductCodes), ",")
	storeIDs := strings.Join(param.StoreIDs, ",")

	r, err := ro().Query(
		fmt.Sprintf(
			query,
			productCodes,
			storeIDs,
		),
		param.Date,
		param.ChannelName,
	)
	if err != nil {
		return nil, err
	}

	return r, nil
}

func (p *PromotionTable) findAllByPromotionChannelQueryParam(query string, param *PromotionQueryParam) (*sql.Rows, error) {
	productCodes := strings.Join(p.toSQLStrings(param.ProductCodes), ",")
	storeIDs := strings.Join(param.StoreIDs, ",")

	r, err := ro().Query(
		fmt.Sprintf(
			query,
			productCodes,
			storeIDs,
		),
		param.Date,
	)
	if err != nil {
		return nil, err
	}

	return r, nil
}

func (p *PromotionTable) findAllByPromotionQuotaQueryParam(query string, param *PromotionQueryParam) (*sql.Rows, error) {
	productCodes := strings.Join(p.toSQLStrings(param.ProductCodes), ",")
	storeIDs := strings.Join(param.StoreIDs, ",")

	r, err := ro().Query(
		fmt.Sprintf(
			query,
			productCodes,
			storeIDs,
		),
		param.Date,
	)
	if err != nil {
		return nil, err
	}

	return r, nil
}

func (p *PromotionTable) findAllByExactDatesAndStoreIDs(dateSQLPredicate interface{}, storeIDs []int64) ([]*PromotionQueryResult, error) {
	qStoreIDs := sq.Or{}
	for _, storeID := range storeIDs {
		qStoreIDs = append(qStoreIDs, sq.Expr(fmt.Sprintf("pd.store_id = %d", storeID)))
	}

	query, _, err := sq.StatementBuilder.PlaceholderFormat(sq.Dollar).
		Select("p.promotion_code, p.new_start_date, p.new_last_date, pd.product_code, pd.store_id, pd.type, c.name, pd.rules, pd.actions").
		From("aloha_promotion_details pd").
		JoinClause("INNER JOIN aloha_promotions p ON p.id = pd.promotion_id").
		JoinClause("INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id").
		Where(qStoreIDs).
		Where(dateSQLPredicate).
		ToSql()
	if err != nil {
		log.For("db", "findAllByExactDatesAndStoreIDs").Error(err)
		return nil, err
	}

	rows, err := ro().Query(query)
	if err != nil {
		log.For("db", "findAllByExactDatesAndStoreIDs").Error(err)
		return nil, err
	}

	var result []*PromotionQueryResult
	defer rows.Close()
	for rows.Next() {
		r := &PromotionQueryResult{}
		err = rows.Scan(
			&r.PromotionCode,
			&r.StartDate,
			&r.LastDate,
			&r.ProductCode,
			&r.StoreID,
			&r.Type,
			&r.Channel,
			&r.Rules,
			&r.Actions,
		)
		if err != nil {
			log.For("db", "findAllByExactDatesAndStoreIDs").Error(err)
			return nil, err
		}

		result = append(result, r)
	}

	return result, nil
}

func (p *PromotionTable) toSQLStrings(ss []string) []string {
	r := make([]string, len(ss))
	for i, s := range ss {
		r[i] = fmt.Sprintf("'%s'", s)
	}

	return r
}

func (p *PromotionTable) FindIdAndPromoCodeByPromoCode(promotionCodes []string) (map[string]int, error) {
	if len(promotionCodes) <= 0 {
		return nil, nil
	}

	c := make([]string, len(promotionCodes))
	for i, data := range promotionCodes {
		c[i] = fmt.Sprintf("'%s'", data)
	}
	pCodes := strings.Join(c, ",")

	mapPromoCode := make(map[string]int, len(promotionCodes))
	query, ok := loadQueries(promotionAssetTag)["get-id-and-promotion-code-by-promotion-code"]
	if !ok {
		return nil, errors.New("Query not found")
	}

	query = fmt.Sprintf(query, pCodes)
	r, err := db.Query(query)
	if err != nil {
		log.For("db", "FindIdAndPromoCodeByPromoCode").Error(err)
		return nil, err
	}

	for r.Next() {
		promotion := &PromotionBase{}
		err = r.Scan(
			&promotion.ID,
			&promotion.PromotionCode,
		)
		if err != nil {
			continue
		}
		mapPromoCode[promotion.PromotionCode] = int(promotion.ID)
	}

	return mapPromoCode, err
}

func (p *PromotionTable) GetIdByPromotionCode(promotionCode string) (int, error) {
	queriPromotion := loadQueries(promotionAssetTag)
	var id int
	err := ro().QueryRow(queriPromotion["get-id-promotion-by-promotion-code"], promotionCode).Scan(&id)
	if err != nil {
		log.For("db", "GetIdPromotionByPromotionCode").Error(err)
		return id, err
	}

	return id, err
}

func (p *PromotionTable) GetExclusivePromoProductCode(param *ExclusivePromotionQueryParam) ([]string, error) {
	result := []string{}
	if len(param.Channel) == 0 || param.StoreID == 0 {
		return result, nil
	}

	sources := []string{}
	for _, sourceID := range param.SourceIDs {
		sources = append(sources, fmt.Sprint(sourceID))
	}
	sourceIDs := strings.Join(sources, ",")

	queryPromo := loadQueries(promotionAssetTag)
	rows, err := ro().Query(
		queryPromo["get-exclusive-promo"],
		param.Date,
		param.StoreID,
		sourceIDs,
		param.Channel,
	)
	if err != nil {
		return result, err
	}

	for rows.Next() {
		var s string
		if err := rows.Scan(&s); err != nil {
			return result, err
		}
		result = append(result, s)
	}

	if err = rows.Err(); err != nil {
		log.For("db", "FindAllActive").Error(err.Error())
		return result, err
	}

	return result, nil
}
