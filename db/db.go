package db

import (
	"database/sql"
	"errors"
	"math/rand"
	"net/url"
	"sync"

	"github.com/XSAM/otelsql"
	_ "github.com/lib/pq"
	ql "github.com/nleof/goyesql"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/config"
	"happyfresh.io/product-promotion/lib/utils"
)

var (
	dbLock sync.Mutex
	db     *sql.DB
	ros    []*sql.DB
)

// Open opens a postgresql connection to a dsn.
// Clients will need to call this before calling query.
func Open(dsn string, dsns ...string) (err error) {
	drName, _ := otelsql.Register("postgres")

	dbLock.Lock()
	d, _ := url.Parse(dsn)
	d.User = url.UserPassword(d.User.Username(), "-FILTERED-")
	log.For("db", "Open").Infof("Opening database on %s", d.String())

	db, err = sql.Open(drName, dsn)
	if err != nil {
		log.For("db", "Open").Errorf(err.Error())
		return err
	}
	dbLock.Unlock()

	db.SetMaxOpenConns(config.DatabaseMaxOpenConnection())
	db.SetMaxIdleConns(config.DatabaseMaxIdleConnection())

	for _, dsn := range dsns {
		db, err := sql.Open(drName, dsn)
		if err != nil {
			log.For("db", "OpenRO").Errorf(err.Error())
			continue
		}

		db.SetMaxOpenConns(config.DatabaseMaxOpenConnection())
		db.SetMaxIdleConns(config.DatabaseMaxIdleConnection())

		ros = append(ros, db)
	}

	return nil
}

func loadQueries(asset string) ql.Queries {
	data, err := utils.Asset(asset)
	if err != nil {
		log.For("db", "loadQueries").Fatalf("DB : resources not found (forgoten import?), for %s - error %s", asset, err.Error())
	}

	queries, err := ql.ParseBytes(data)
	if err != nil {
		log.For("db", "loadQueries").Fatalf("DB : resources failed on parse for %s - error %s", asset, err.Error())
	}
	return queries
}

func ro() *sql.DB {
	n := len(ros)
	if n == 1 {
		return ros[0]
	}
	if n > 0 {
		return ros[rand.Intn(n)]
	}

	return db
}

func tx(f func(*sql.Tx) error) error {
	t, err := db.Begin()
	if err != nil {
		log.For("db", "tx").Error(err)
		return err
	}

	err = f(t)

	if err != nil {
		log.For("db", "tx").Error(err)
		t.Rollback()
		return err
	}

	t.Commit()

	return nil
}

func GetDB() *sql.DB {
	return db
}

// TruncateDB truncate all tables in the DB. Only runs in test Env.
func TruncateDB() error {
	if config.Env() != "test" {
		return errors.New("Not test environment")
	}

	queries := loadQueries(promotionAssetTag)
	_, err := db.Exec(queries["truncate-db"])
	if err != nil {
		return err
	}

	return nil
}
