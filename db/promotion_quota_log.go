package db

import (
	"database/sql"
	"fmt"

	"happyfresh.io/lib/log"
)

const (
	promotionQuotaLogAssetTag = "resources/sql/5.promotion_quota_log.sql"
)

type PromotionQuotaLog struct {
	ID            int
	PromotionId   int
	PromotionCode string
	ProductCode   string
	Quota         int
	Active        bool
	CreatedAt     sql.NullTime
	UpdatedAt     sql.NullTime
	NoOrder       sql.NullString
	QuantityOrder sql.NullInt32
}

// PromotionQuotaTableLog :nodoc:
type PromotionQuotaTableLog struct {
	Tx *sql.Tx
}

// NewPromotionQuotaLog :nodoc:
func NewPromotionQuotaLog(tx *sql.Tx) *PromotionQuotaTableLog {
	return &PromotionQuotaTableLog{
		Tx: tx,
	}
}

// InsertPromotionQuotaLogRequest :nodoc:
type InsertPromotionQuotaLogRequest struct {
	PromotionID   int
	ProductCode   string
	Quota         int
	Active        bool
	NoOrder       string
	QuantityOrder int
	PromoCode     string
}

// NewInsertPromotionQuotaLogRequest :nodoc:
func NewInsertPromotionQuotaLogRequest(promotionID int, productCode string,
	quota int, active bool, noOrder string, quantityOrder int, promoCode string) InsertPromotionQuotaLogRequest {
	return InsertPromotionQuotaLogRequest{
		PromotionID:   promotionID,
		ProductCode:   productCode,
		Quota:         quota,
		Active:        active,
		NoOrder:       noOrder,
		QuantityOrder: quantityOrder,
		PromoCode:     promoCode,
	}
}

// InsertWithTx :nodoc:
func (p *PromotionQuotaTableLog) InsertWithTx(req InsertPromotionQuotaLogRequest) error {
	var err error
	queriPromotionQuotaLogs := loadQueries(promotionQuotaLogAssetTag)
	_, err = p.Tx.Exec(queriPromotionQuotaLogs["insert-promotion-quota-logs"],
		req.PromotionID, req.ProductCode, req.Quota, req.Active, req.NoOrder, req.QuantityOrder)
	if err != nil {
		err = p.Tx.Rollback()
		if err != nil {
			log.For("db", "InsertWithTx").
				Errorf("rollback Transaction Error %v", err)
			return err
		}

		log.For("db", "InsertQuotaLog").Error(err)
		return err
	}

	return err
}

// FindByProductCodeNNoOrder :nodoc:
func (p *PromotionQuotaTableLog) FindByProductCodeNNoOrder(noOrder, productCode string) (PromotionQuotaLog, error) {
	var promotionData PromotionQuotaLog
	queriPromotionQuotaLogs := loadQueries(promotionQuotaLogAssetTag)
	err := ro().QueryRow(queriPromotionQuotaLogs["get-promotion-quota-logs-by-product-code-noorder"],
		noOrder, productCode).Scan(&promotionData.ProductCode, &promotionData.PromotionId,
		&promotionData.QuantityOrder)
	if err != nil {
		log.For("db", "GetLogByProductCodeNNoOrder").Error(err)
		return promotionData, err
	}

	return promotionData, err
}

// InsertByPromoCodeWithTx :nodoc:
func (p *PromotionQuotaTableLog) InsertByPromoCodeWithTx(req InsertPromotionQuotaLogRequest) error {
	queriPromotionQuotaLogs := loadQueries(promotionQuotaLogAssetTag)
	_, err := p.Tx.Exec(queriPromotionQuotaLogs["insert-promotion-quota-logs-by-promotion-code"],
		req.PromoCode,
		req.ProductCode,
		0,
		req.Active,
		req.NoOrder,
		req.QuantityOrder)

	if err != nil {
		err = p.Tx.Rollback()
		if err != nil {
			log.For("db", "InsertByPromoCodeWithTx").
				Errorf("rollback Transaction Error %v", err)
			return err
		}

		log.For("db", "InsertLogByPromotionCode").Error(err)
		return err
	}

	return err
}

// SumQtyOrderMoreThanZeroNotInProductCode :nodoc:
func (p *PromotionQuotaTableLog) SumQtyOrderMoreThanZeroNotInProductCode(pCodes, noOrder string) ([]PromotionQuotaLog, error) {
	queriPromotionQuotaLogs := loadQueries(promotionQuotaLogAssetTag)
	rows, err := db.Query(fmt.Sprintf(queriPromotionQuotaLogs["get-sum-of-quantity-more-than-zero"], pCodes), noOrder)
	if err != nil {
		log.For("db", "GetSumOfQuantityMoreThanZero").Error(err)
		return []PromotionQuotaLog{}, err
	}

	var promotionDatas []PromotionQuotaLog
	for rows.Next() {
		var promotionData PromotionQuotaLog
		err = rows.Scan(&promotionData.ProductCode, &promotionData.PromotionId, &promotionData.QuantityOrder)
		if err != nil {
			continue
		}

		promotionDatas = append(promotionDatas, promotionData)
	}

	return promotionDatas, err
}
