package test

import (
	"database/sql"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/product-promotion/db"
	"happyfresh.io/product-promotion/lib/rpc"
)

func TestFindAllActivePromo(t *testing.T) {

	t.Run("get promotion by specific date", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		queryParam := &db.PromotionQueryParam{
			ProductCodes: []string{"SKU001", "SKU002"},
			StoreIDs:     []string{strconv.Itoa(9182)},
			ChannelName:  "android",
			Date: sql.NullTime{
				Time:  time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				Valid: true,
			},
		}
		queried, err := repo.FindAllActive(queryParam)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(2, len(queried))
		assert.Equal("promo1", queried[0].PromotionCode)
		assert.Equal("30", queried[0].Actions.Map[rpc.ActionPercentage].String)

		truncateDatabase()
	})

	t.Run("get HC promotion", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		queryParam := &db.PromotionQueryParam{
			ProductCodes: []string{"SKU001", "SKU002"},
			StoreIDs:     []string{strconv.Itoa(9182)},
			ChannelName:  "hc",
			Date: sql.NullTime{
				Time:  time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				Valid: true,
			},
		}
		queried, err := repo.FindAllActive(queryParam)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(2, len(queried))
		assert.Equal("promo6", queried[0].PromotionCode)
		assert.Equal("30", queried[0].Actions.Map[rpc.ActionPercentage].String)

		truncateDatabase()
	})

	t.Run("get lineman promotion", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		queryParam := &db.PromotionQueryParam{
			ProductCodes: []string{"SKU001", "SKU002"},
			StoreIDs:     []string{strconv.Itoa(9182)},
			ChannelName:  "lineman",
			Date: sql.NullTime{
				Time:  time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				Valid: true,
			},
		}
		queried, err := repo.FindAllActive(queryParam)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(2, len(queried))
		assert.Equal("promo7", queried[0].PromotionCode)
		assert.Equal("40", queried[0].Actions.Map[rpc.ActionPercentage].String)

		truncateDatabase()
	})

	t.Run("get bukalapak promotion", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		queryParam := &db.PromotionQueryParam{
			ProductCodes: []string{"SKU001", "SKU002"},
			StoreIDs:     []string{strconv.Itoa(9182)},
			ChannelName:  "bukalapak",
			Date: sql.NullTime{
				Time:  time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				Valid: true,
			},
		}
		queried, err := repo.FindAllActive(queryParam)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(2, len(queried))
		assert.Equal("promo10", queried[0].PromotionCode)
		assert.Equal("40", queried[0].Actions.Map[rpc.ActionPercentage].String)

		truncateDatabase()
	})

	t.Run("get dana promotion", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		queryParam := &db.PromotionQueryParam{
			ProductCodes: []string{"SKU001", "SKU002"},
			StoreIDs:     []string{strconv.Itoa(9182)},
			ChannelName:  "dana",
			Date: sql.NullTime{
				Time:  time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				Valid: true,
			},
		}
		queried, err := repo.FindAllActive(queryParam)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(2, len(queried))
		assert.Equal("promo11", queried[0].PromotionCode)
		assert.Equal("40", queried[0].Actions.Map[rpc.ActionPercentage].String)

		truncateDatabase()
	})
}

func TestDeleteAll(t *testing.T) {
	createPromotion()

	t.Run("delete a specific promotion", func(t *testing.T) {

		repo := db.NewPromotionTable()

		promoCodes := []string{"promo2"}

		queried, err := repo.DeleteAll(promoCodes)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal([]int{2}, queried)
	})

	truncateDatabase()
}

func TestFindAllOverlappingPromotion(t *testing.T) {

	t.Run("check overlapping promotion", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		cases := []struct {
			promoCode, productCode string
			storeId, channelId     int
			startDate, lastDate    string
			count                  int
		}{
			{"new-promo", "SKU001", 9182, 1, "2020-01-01", "2020-01-01", 1},
			{"new-promo", "SKU001", 9182, 1, "2020-01-01", "2020-01-02", 2},
			{"new-promo", "SKU001", 9182, 1, "2020-01-02", "2020-01-01", 2},
			{"new-promo", "SKU001", 9182, 1, "2020-01-02", "2020-01-02", 1},
			{"promo1", "SKU001", 9182, 1, "2020-01-01", "2020-01-01", 0},
			{"promo2", "SKU001", 9182, 1, "2020-01-02", "2020-01-02", 0},
		}

		for _, c := range cases {
			result, err := repo.FindAllOverlapping(c.promoCode, c.productCode, c.storeId, c.channelId, c.startDate, c.lastDate)
			assert := assert.New(t)
			assert.Nil(err)
			assert.Equal(c.count, len(result))
		}
		truncateDatabase()
	})

	t.Run("save overlapping promotion should return error", func(t *testing.T) {
		createPromotion()

		// Create new promotion with identical promo detail as "promo1"
		now := time.Now().Format("2006-01-02")

		pds := make([]*db.PromotionDetail, 1)
		pds[0] = &db.PromotionDetail{
			ProductCode: "SKU001",
			StoreID:     9182,
			ChannelID:   1,
			Type:        "StockItem",
			Rules: map[string]string{
				rpc.RuleMOQ: "5",
			},
			Actions: map[string]string{
				rpc.ActionPercentage: "30",
			},
		}
		date := "2020-01-01"
		promoBase := &db.PromotionBase{
			PromotionCode: "new-promo",
			SourceID:      int(1),
			StartDate:     date,
			LastDate:      date,
			CreatedAt:     now,
			UpdatedAt:     now,
		}

		promo := &db.Promotion{
			PromotionBase:    promoBase,
			PromotionDetails: pds,
		}

		promos := []*db.Promotion{promo}

		repo := db.NewPromotionTable()

		_, err := repo.SaveAll(promos)
		assert := assert.New(t)
		assert.Error(err)
		assert.EqualError(err, "Overlapping promotions detected")

		truncateDatabase()
	})
}

func TestGetPromotionDetailInfoWithFilter(t *testing.T) {
	t.Run("get promotion by a set of filter", func(t *testing.T) {
		createPromotion()

		repo := db.NewPromotionTable()

		cases := []struct {
			storeIds                                    []int64
			promoCode, productCode, startDate, lastDate string
			promotionSource, size, offset               int64
			count                                       int
		}{
			{[]int64{9182}, "promo1", "SKU001", "2020-01-01", "2020-01-01", 1, 10, 0, 1},
			{[]int64{9182}, "promo1", "SKU002", "2020-01-01", "2020-01-01", 1, 10, 0, 1},
			{[]int64{9182}, "promo1", "SKU001", "2020-01-01", "2020-01-02", 1, 10, 0, 1},
			{[]int64{9182}, "promo1", "SKU002", "2020-01-01", "2020-01-02", 1, 10, 0, 1},
			{[]int64{9182}, "promo1", "NOSKU", "2020-01-01", "2020-01-02", 1, 10, 0, 0},
			{[]int64{9182}, "promo1", "SKU002", "2020-01-01", "2020-01-02", 9, 10, 0, 0},
			{[]int64{9182}, "no-promo", "SKU001", "2020-01-01", "2020-01-02", 1, 10, 0, 0},
		}

		for _, c := range cases {
			_, count, err := repo.FindAllByFilters(c.storeIds, c.promoCode, c.productCode, c.startDate, c.lastDate, c.promotionSource, c.size, c.offset)
			assert := assert.New(t)
			assert.Nil(err)
			assert.Equal(c.count, count)
		}

		truncateDatabase()
	})
}

func TestFindAllStartingPromotions(t *testing.T) {
	createPromotion()

	repo := db.NewPromotionTable()

	repo.DeleteAllWithCustomTime([]string{"promo4"}, "2020-01-01 01:00:00")

	res, err := repo.FindAllStarting([]string{"2019-12-30"}, []int64{9183})
	assert := assert.New(t)
	assert.Nil(err)
	assert.Equal(1, len(res))

	truncateDatabase()
}

func TestFindAllOnGoingPromotions(t *testing.T) {
	createPromotion()

	repo := db.NewPromotionTable()

	repo.DeleteAllWithCustomTime([]string{"promo4"}, "2020-01-03 01:00:00")

	res, err := repo.FindAllOnGoing([]string{"2020-01-01"}, []int64{9182, 9183})
	assert := assert.New(t)
	assert.Nil(err)
	assert.Equal(3, len(res))

	truncateDatabase()
}

func TestGetPromotedProductCodes(t *testing.T) {
	createPromotion()

	repo := db.NewPromotionTable()

	res, err := repo.GetPromotedProductCodes(9182, "2020-01-15", "android")
	assert := assert.New(t)
	assert.Nil(err)
	assert.Equal(1, len(res))
	assert.Equal([]string{"SKU003"}, res)

	res, err = repo.GetPromotedProductCodes(9182, "2020-01-15", "hc")
	assert.Nil(err)
	assert.Equal(3, len(res))
	assert.Equal([]string{"SKU001", "SKU002", "SKU003"}, res)

	res, err = repo.GetPromotedProductCodes(9182, "2020-01-15", "lineman")
	assert.Nil(err)
	assert.Equal(3, len(res))
	assert.Equal([]string{"SKU001", "SKU002", "SKU003"}, res)
}

func TestFindAllEndingPromotions(t *testing.T) {
	createPromotion()

	repo := db.NewPromotionTable()

	repo.DeleteAllWithCustomTime([]string{"promo4"}, "2020-01-03 01:00:00")

	res, err := repo.FindAllEnding([]string{"2020-01-15", "2020-01-03"}, []int64{9183})
	assert := assert.New(t)
	assert.Nil(err)
	assert.Equal(2, len(res))

	truncateDatabase()
}

func TestDeletePromoBasedOnSKU(t *testing.T) {

	t.Run("delete a promotion based on SKU", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		productCode := "SKU001"
		storeIDs := []int64{}
		promotionCodes := []string{}
		channels := []string{}

		queried, err := repo.DeletePromoBasedOnSKU(productCode, storeIDs, promotionCodes, channels)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal([]int{1, 3, 5, 7, 9, 11}, queried)

		truncateDatabase()
	})

	t.Run("delete a promotion based on SKU, store ID", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		productCode := "SKU001"
		storeIDs := []int64{9182}
		promotionCodes := []string{}
		channels := []string{}

		queried, err := repo.DeletePromoBasedOnSKU(productCode, storeIDs, promotionCodes, channels)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal([]int{1, 3, 5, 9, 11}, queried)

		truncateDatabase()
	})

	t.Run("delete a promotion based on SKU, store ID, promotion code", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		productCode := "SKU001"
		storeIDs := []int64{9182}
		promotionCodes := []string{"promo1"}
		channels := []string{}

		queried, err := repo.DeletePromoBasedOnSKU(productCode, storeIDs, promotionCodes, channels)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal([]int{1}, queried)

		truncateDatabase()
	})

	t.Run("delete a promotion based on SKU, store ID, promotion code, and channel", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		productCode := "SKU001"
		storeIDs := []int64{9182}
		promotionCodes := []string{"promo7"}
		channels := []string{"lineman"}

		queried, err := repo.DeletePromoBasedOnSKU(productCode, storeIDs, promotionCodes, channels)

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal([]int{11}, queried)

		truncateDatabase()
	})

}

func TestGetPromotionDetailByPromotionCode(t *testing.T) {
	t.Run("get promotion detail by promotion code with no deleted_at", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()

		promotionCode := "promo9"
		expected := make(map[string]string)
		expected["PromotionCode"] = "promo9"
		expected["StartDate"] = "2019-12-30T00:00:00Z"
		expected["LastDate"] = "2021-01-15T00:00:00Z"
		expected["ProductCode"] = "SKU004"
		expected["StoreID"] = "9184"
		expected["Type"] = "StockItem"
		expected["Channel"] = "android"
		expected["Rules"] = "5"
		expected["Actions"] = "40"
		expected["PromotionsDeletedAt"] = ""

		queried, err := repo.FindPromotionDetailsByPromoCode(promotionCode)

		rules := make(map[string]string)
		for k, v := range queried[0].Rules.Map {
			rules[k] = v.String
		}

		actions := make(map[string]string)
		for k, v := range queried[0].Actions.Map {
			actions[k] = v.String
		}

		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(expected["PromotionCode"], queried[0].PromotionCode)
		assert.Equal(expected["StartDate"], queried[0].StartDate)
		assert.Equal(expected["LastDate"], queried[0].LastDate)
		assert.Equal(expected["ProductCode"], queried[0].ProductCode)
		assert.Equal(expected["StoreID"], strconv.Itoa(queried[0].StoreID))
		assert.Equal(expected["Type"], queried[0].Type)
		assert.Equal(expected["Channel"], queried[0].Channel)
		assert.Equal(expected["Rules"], rules["moq"])
		assert.Equal(expected["Actions"], actions["percentage"])
		assert.Equal(expected["PromotionsDeletedAt"], queried[0].PromotionsDeletedAt)

		truncateDatabase()
	})

	t.Run("get promotion detail by promotion code with unknown promotion code", func(t *testing.T) {
		createPromotion()
		repo := db.NewPromotionTable()
		promotionCode := "x"
		expected := []*db.PromotionDetailInfo(nil)

		queried, err := repo.FindPromotionDetailsByPromoCode(promotionCode)
		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(queried, expected)

		truncateDatabase()
	})
}

func createPromotion() {
	channel, _ := MockPromotionChannel(1, "android")
	source, _ := MockPromotionSource(int64(1), "marketing")
	channelHC, _ := MockPromotionChannel(6, "hc")
	channelLineman, _ := MockPromotionChannel(7, "lineman")
	channelBukalapak, _ := MockPromotionChannel(8, "bukalapak")
	channelDana, _ := MockPromotionChannel(9, "dana")
	now := time.Now().Format("2006-01-02")

	storeID := 9182
	storeID2 := 9183
	pc1 := "SKU001"
	pc2 := "SKU002"
	pc3 := "SKU003"

	pds1 := make([]*db.PromotionDetail, 2)
	pds1[0] = &db.PromotionDetail{
		ProductCode: pc1,
		StoreID:     storeID,
		ChannelID:   channel.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "30",
		},
	}
	pds1[1] = &db.PromotionDetail{
		ProductCode: pc2,
		StoreID:     storeID,
		ChannelID:   channel.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "30",
		},
	}

	date1 := "2020-01-01"
	promoBase1 := &db.PromotionBase{
		PromotionCode: "promo1",
		SourceID:      int(source.ID),
		StartDate:     date1,
		LastDate:      date1,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	promo1 := &db.Promotion{
		PromotionBase:    promoBase1,
		PromotionDetails: pds1,
	}

	pds2 := make([]*db.PromotionDetail, 2)
	pds2[0] = &db.PromotionDetail{
		ProductCode: pc1,
		StoreID:     storeID,
		ChannelID:   channel.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "50",
		},
	}
	pds2[1] = &db.PromotionDetail{
		ProductCode: pc2,
		StoreID:     storeID,
		ChannelID:   channel.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "50",
		},
	}

	date2 := "2020-01-02"
	promoBase2 := &db.PromotionBase{
		PromotionCode: "promo2",
		SourceID:      int(source.ID),
		StartDate:     date2,
		LastDate:      date2,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
	promo2 := &db.Promotion{
		PromotionBase:    promoBase2,
		PromotionDetails: pds2,
	}

	date3 := "2020-02-02"
	promoBase3 := &db.PromotionBase{
		PromotionCode: "promo3",
		SourceID:      int(source.ID),
		StartDate:     date3,
		LastDate:      date3,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
	promo3 := &db.Promotion{
		PromotionBase:    promoBase3,
		PromotionDetails: pds2,
	}

	pds4 := make([]*db.PromotionDetail, 1)
	pds4[0] = &db.PromotionDetail{
		ProductCode: pc1,
		StoreID:     storeID2,
		ChannelID:   channel.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "30",
		},
	}
	promoBase4 := &db.PromotionBase{
		PromotionCode: "promo4",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2020-01-10",
		CreatedAt:     now,
		UpdatedAt:     now,
	}
	promo4 := &db.Promotion{
		PromotionBase:    promoBase4,
		PromotionDetails: pds4,
	}

	pds5 := make([]*db.PromotionDetail, 1)
	pds5[0] = &db.PromotionDetail{
		ProductCode: pc2,
		StoreID:     storeID2,
		ChannelID:   channel.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "30",
		},
	}
	promoBase5 := &db.PromotionBase{
		PromotionCode: "promo5",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2020-01-15",
		CreatedAt:     now,
		UpdatedAt:     now,
	}
	promo5 := &db.Promotion{
		PromotionBase:    promoBase5,
		PromotionDetails: pds5,
	}

	pds6 := make([]*db.PromotionDetail, 2)
	pds6[0] = &db.PromotionDetail{
		ProductCode: pc1,
		StoreID:     storeID,
		ChannelID:   channelHC.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "30",
		},
	}
	pds6[1] = &db.PromotionDetail{
		ProductCode: pc2,
		StoreID:     storeID,
		ChannelID:   channelHC.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "30",
		},
	}
	promoBase6 := &db.PromotionBase{
		PromotionCode: "promo6",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2020-01-15",
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	promo6 := &db.Promotion{
		PromotionBase:    promoBase6,
		PromotionDetails: pds6,
	}

	pds7 := make([]*db.PromotionDetail, 2)
	pds7[0] = &db.PromotionDetail{
		ProductCode: pc1,
		StoreID:     storeID,
		ChannelID:   channelLineman.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	pds7[1] = &db.PromotionDetail{
		ProductCode: pc2,
		StoreID:     storeID,
		ChannelID:   channelLineman.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	promoBase7 := &db.PromotionBase{
		PromotionCode: "promo7",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2020-01-15",
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	promo7 := &db.Promotion{
		PromotionBase:    promoBase7,
		PromotionDetails: pds7,
	}

	pds8 := make([]*db.PromotionDetail, 3)
	pds8[0] = &db.PromotionDetail{
		ProductCode: pc3,
		StoreID:     storeID,
		ChannelID:   channelLineman.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	pds8[1] = &db.PromotionDetail{
		ProductCode: pc3,
		StoreID:     storeID,
		ChannelID:   1,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	pds8[2] = &db.PromotionDetail{
		ProductCode: pc3,
		StoreID:     storeID,
		ChannelID:   channelHC.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	promoBase8 := &db.PromotionBase{
		PromotionCode: "promo8",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2020-01-15",
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	promo8 := &db.Promotion{
		PromotionBase:    promoBase8,
		PromotionDetails: pds8,
	}

	pds9 := make([]*db.PromotionDetail, 1)
	pds9[0] = &db.PromotionDetail{
		ProductCode: "SKU004",
		StoreID:     9184,
		ChannelID:   1,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}

	promoBase9 := &db.PromotionBase{
		PromotionCode: "promo9",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2021-01-15",
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	promo9 := &db.Promotion{
		PromotionBase:    promoBase9,
		PromotionDetails: pds9,
	}

	pds10 := make([]*db.PromotionDetail, 2)
	pds10[0] = &db.PromotionDetail{
		ProductCode: pc1,
		StoreID:     storeID,
		ChannelID:   channelBukalapak.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	pds10[1] = &db.PromotionDetail{
		ProductCode: pc2,
		StoreID:     storeID,
		ChannelID:   channelBukalapak.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	promoBase10 := &db.PromotionBase{
		PromotionCode: "promo10",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2020-01-15",
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	promo10 := &db.Promotion{
		PromotionBase:    promoBase10,
		PromotionDetails: pds10,
	}

	pds11 := make([]*db.PromotionDetail, 2)
	pds11[0] = &db.PromotionDetail{
		ProductCode: pc1,
		StoreID:     storeID,
		ChannelID:   channelDana.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	pds11[1] = &db.PromotionDetail{
		ProductCode: pc2,
		StoreID:     storeID,
		ChannelID:   channelDana.ID,
		Type:        "StockItem",
		Rules: map[string]string{
			rpc.RuleMOQ: "5",
		},
		Actions: map[string]string{
			rpc.ActionPercentage: "40",
		},
	}
	promoBase11 := &db.PromotionBase{
		PromotionCode: "promo11",
		SourceID:      int(source.ID),
		StartDate:     "2019-12-30",
		LastDate:      "2020-01-15",
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	promo11 := &db.Promotion{
		PromotionBase:    promoBase11,
		PromotionDetails: pds11,
	}

	promos := []*db.Promotion{promo1, promo2, promo3, promo4, promo5, promo6, promo7, promo8, promo9, promo10, promo11}

	repo := db.NewPromotionTable()

	_, _ = repo.SaveAll(promos)
}
