package test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/product-promotion/db"
)

func MockPromotionSource(id int64, source string) (*db.PromotionSource, error) {
	src := &db.PromotionSource{
		ID:   int64(id),
		Name: source,
	}

	err := db.NewPromotionSourceTable().Save(src)
	if err != nil {
		return nil, err
	}

	return src, nil
}

func TestGetAllPromotionSource(t *testing.T) {
	t.Run("get all promotion source", func(t *testing.T) {
		source1, _ := MockPromotionSource(1, "marketing")
		source2, _ := MockPromotionSource(2, "happyads")
		source3, _ := MockPromotionSource(3, "content")
		source4, _ := MockPromotionSource(4, "internal")

		repo := db.NewPromotionSource()

		result, err := repo.GetAllPromotionSource()
		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(4, len(result))
		assert.Equal(source1.Name, result[0].Name)
		assert.Equal(source2.Name, result[1].Name)
		assert.Equal(source3.Name, result[2].Name)
		assert.Equal(source4.Name, result[3].Name)
	})

	truncateDatabase()
}
