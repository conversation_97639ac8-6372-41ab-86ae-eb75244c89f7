package test

import (
	"os"
	"testing"

	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/config"
	"happyfresh.io/product-promotion/db"
)

func openDatabase() error {
	err := db.Open(config.DatabaseDSN())
	if err != nil {
		log.For("test", "openDatabase").Error(err.<PERSON>rror())
		return err
	}
	return nil
}

func truncateDatabase() error {
	err := db.TruncateDB()
	if err != nil {
		log.For("test", "truncateDatabase").Error(err.Error())
		return err
	}
	return nil
}

func TestMain(m *testing.M) {
	err := openDatabase()
	if err != nil {
		os.Exit(1)
	}

	err = truncateDatabase()
	if err != nil {
		os.Exit(1)
	}

	code := m.Run()

	os.Exit(code)
}
