package test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"happyfresh.io/product-promotion/db"
)

func MockPromotionChannel(id int, name string) (*db.PromotionChannel, error) {
	ch := &db.PromotionChannel{
		ID:   id,
		Name: name,
	}

	err := db.NewPromotionChannelTable().Save(ch)
	if err != nil {
		return nil, err
	}

	return ch, nil
}

func TestGetAllPromotionChannel(t *testing.T) {
	t.Run("get all promotion channel", func(t *testing.T) {
		channel1, _ := MockPromotionChannel(1, "android")
		channel2, _ := MockPromotionChannel(2, "ios")
		channel3, _ := MockPromotionChannel(3, "webapp")
		channel4, _ := MockPromotionChannel(4, "grabfresh")

		repo := db.NewPromotionChannelTable()

		result, err := repo.GetAllPromotionChannel()
		assert := assert.New(t)
		assert.Nil(err)
		assert.Equal(4, len(result))
		assert.Equal(channel1.Name, result[0].Name)
		assert.Equal(channel2.Name, result[1].Name)
		assert.Equal(channel3.Name, result[2].Name)
		assert.Equal(channel4.Name, result[3].Name)

	})

	truncateDatabase()
}
