package db

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"happyfresh.io/lib/log"
)

const (
	quotaAssetTag = "resources/sql/4.promotion_quota.sql"
)

// PromotionQuota :nodoc:
type PromotionQuota struct {
	ID            int
	PromotionId   int
	PromotionCode string
	ProductCode   string
	Quota         int
	Active        bool
	CreatedAt     sql.NullTime
	UpdatedAt     sql.NullTime
}

// PromotionQuotaError :nodoc:
type PromotionQuotaError struct {
	*PromotionQuota
	ErrorMessages string
}

// PromotionQuotaTable :nodoc:
type PromotionQuotaTable struct {
	Tx *sql.Tx
}

// NewPromotionQuota :nodoc:
func NewPromotionQuota(tx *sql.Tx) *PromotionQuotaTable {
	return &PromotionQuotaTable{Tx: tx}
}

// InsertToPromotionQuota :nodoc:
func (p *PromotionQuotaTable) InsertToPromotionQuota(promotionCode *string, productCode *string, quota *int32, active *bool) error {
	queries := loadQueries(quotaAssetTag)
	err := tx(func(db *sql.Tx) error {
		_, err := db.Exec(queries["insert-single-promotion-quota"], *promotionCode, *productCode, *quota, *active)
		if err != nil {
			log.For("db", "QuotaTable#Save").Error(err.Error())
			return err
		}
		return nil
	})

	if err != nil {
		log.For("db", "QuotaTable#Save").Error(err.Error())
		return err
	}

	return nil
}

// InsertAllData :nodoc:
func (p *PromotionQuotaTable) InsertAllData(promotionSlice *[]PromotionQuota, promoMap map[string]int) ([]*PromotionQuotaError, error) {
	queries := loadQueries(quotaAssetTag)
	var promoQuotaErrors []*PromotionQuotaError
	err := tx(func(db *sql.Tx) error {
		insertPromotionQuota, err := db.Prepare(queries["insert-single-promotion-quota"])
		if err != nil {
			log.For("db", "SaveAll").Error(err)
			return err
		}

		for _, promotion := range *promotionSlice {
			_, found := promoMap[promotion.PromotionCode]
			if !found {
				promoQuotaErrors = append(promoQuotaErrors, &PromotionQuotaError{
					PromotionQuota: &PromotionQuota{PromotionCode: promotion.PromotionCode,
						ProductCode: promotion.ProductCode,
						Active:      promotion.Active,
						Quota:       promotion.Quota},
					ErrorMessages: fmt.Sprintf("Failed Upsert Product Code : %s , Promo Code : %s , Quota : %d , Active : % v . Error Message : %v",
						promotion.PromotionCode, promotion.ProductCode, promotion.Quota, promotion.Active, "promotion code not valid"),
				})
				continue
			}
			_, err = insertPromotionQuota.Exec(
				promoMap[promotion.PromotionCode],
				promotion.ProductCode,
				promotion.Quota,
				promotion.Active,
			)

			if err != nil {
				log.For("db", "SaveAll").Error(err.Error())
				promoQuotaErrors = append(promoQuotaErrors, &PromotionQuotaError{
					PromotionQuota: &PromotionQuota{PromotionCode: promotion.ProductCode,
						ProductCode: promotion.ProductCode,
						Active:      promotion.Active,
						Quota:       promotion.Quota},
					ErrorMessages: fmt.Sprintf("Failed Upsert Product Code : %s , Promo Code : %s , Quota : %d , Active : % v . Error Message : %v",
						promotion.PromotionCode, promotion.ProductCode, promotion.Quota, promotion.Active, err.Error()),
				})
				continue
			}
		}

		err = insertPromotionQuota.Close()
		if err != nil {
			log.For("db", "SaveAll").Error(err.Error())
			return err
		}

		return nil
	})

	if err != nil {
		log.For("db", "QuotaTable#Save").Error(err.Error())
		return promoQuotaErrors, err
	}

	return promoQuotaErrors, nil
}

// GetQuotaByFilter :nodoc:
func (p *PromotionQuotaTable) GetQuotaByFilter(promotionCode *string, productCode *string, quota *int32, active *bool) ([]*PromotionQuota, error) {
	promotionQuotas := []*PromotionQuota{}
	var flagProdCode bool
	var flagPromoCode bool

	if strings.TrimSpace(*productCode) != "" {
		flagProdCode = true
	}

	if strings.TrimSpace(*promotionCode) != "" {
		flagPromoCode = true
	}

	if flagProdCode && flagPromoCode {
		query := loadQueries(quotaAssetTag)["get-promotion-quota-by-product-code-and-promotion-and-active"]
		r, err := ro().Query(query, *promotionCode, *productCode, *active)
		defer r.Close()
		if err != nil {
			return promotionQuotas, err
		}

		for r.Next() {
			var data = PromotionQuota{}
			var err = r.Scan(&data.PromotionCode, &data.ProductCode, &data.Quota, &data.Active, &data.CreatedAt, &data.UpdatedAt)
			if err != nil {
				continue
			}

			promotionQuotas = append(promotionQuotas, &data)
		}
		return promotionQuotas, err
	} else if flagProdCode && !flagPromoCode {
		query := loadQueries(quotaAssetTag)["get-promotion-quota-by-product-code-and-active"]
		r, err := ro().Query(query, *productCode, *active)
		defer r.Close()
		if err != nil {
			return promotionQuotas, err
		}

		for r.Next() {
			var data = PromotionQuota{}
			var err = r.Scan(&data.PromotionCode, &data.ProductCode, &data.Quota, &data.Active, &data.CreatedAt, &data.UpdatedAt)
			if err != nil {
				continue
			}

			promotionQuotas = append(promotionQuotas, &data)
		}

		return promotionQuotas, err
	} else if !flagProdCode && flagPromoCode {
		query := loadQueries(quotaAssetTag)["get-promotion-quota-by-promo-code-and-active"]
		r, err := ro().Query(query, *promotionCode, *active)
		defer r.Close()
		if err != nil {
			return promotionQuotas, err
		}

		for r.Next() {
			var data = PromotionQuota{}
			var err = r.Scan(&data.PromotionCode, &data.ProductCode, &data.Quota, &data.Active, &data.CreatedAt, &data.UpdatedAt)
			if err != nil {
				continue
			}

			promotionQuotas = append(promotionQuotas, &data)
		}
		return promotionQuotas, err
	} else {
		query := loadQueries(quotaAssetTag)["get-promotion-quota-by-active"]
		r, err := ro().Query(query, *active)
		defer r.Close()
		if err != nil {
			return promotionQuotas, err
		}

		for r.Next() {
			var data = PromotionQuota{}
			var err = r.Scan(&data.PromotionCode, &data.ProductCode, &data.Quota, &data.Active, &data.CreatedAt, &data.UpdatedAt)
			if err != nil {
				continue
			}

			promotionQuotas = append(promotionQuotas, &data)
		}
		return promotionQuotas, err
	}
}

// UpdateQuotaWithTx :nodoc:
func (p *PromotionQuotaTable) UpdateQuotaByPromoIDNProdCodeWithTx(promoID int, productCode string, promoQuantityOrder int) error {
	var err error
	queriPromotionQuota := loadQueries(quotaAssetTag)
	_, err = p.Tx.Exec(queriPromotionQuota["update-promotion-quota"],
		promoQuantityOrder,
		promoID,
		productCode)
	if err != nil {
		err = p.Tx.Rollback()
		if err != nil {
			log.For("db", "UpdatePromotionQuotaWithTx").
				Errorf("time error %v", err)
		}
		log.For("db", "UpdatePromotionQuota").Error(err)
		return err
	}

	return err
}

// GetActiveByDatePromotionIDProductCode :nodoc:
func (p *PromotionQuotaTable) GetActiveByDatePromotionIDProductCode(promotionId int, productCode string) (bool, error) {
	queriPromotionQuota := loadQueries(quotaAssetTag)
	var dateTime sql.NullTime
	err := dateTime.Scan(time.Now().UTC())
	if err != nil {
		log.For("db", "GetActivePromotionByPromotionID").
			Errorf("time error %v", err)
		return false, err
	}

	var active bool
	err = ro().QueryRow(queriPromotionQuota["get-active-promotion-by-promotion-id"], dateTime, promotionId, productCode).Scan(&active)
	if err != nil {
		return false, err
	}

	return active, err
}

// GetQuotaByPromotionID :nodoc:
func (p *PromotionQuotaTable) GetQuotaByPromotionIDNProdCode(promotionId int, productCode string) (int, error) {
	queriPromotionQuota := loadQueries(quotaAssetTag)
	var dateTime sql.NullTime
	err := dateTime.Scan(time.Now().UTC())
	if err != nil {
		log.For("Handler", "ConsumerGroupHandler").
			Errorf("time error %v", err)
		return 0, err
	}

	var quota int
	err = db.QueryRow(queriPromotionQuota["get-quota-promotion-by-promotion-id"], dateTime, promotionId, productCode).Scan(&quota)
	if err != nil {
		return quota, err
	}

	return quota, err
}
