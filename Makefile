CWD := ${CURDIR}
BINDATA := $(shell command -v go-bindata 2> /dev/null)
VERSION := $(shell cat Version)
BRANCH ?= $(shell git rev-parse --abbrev-ref HEAD)
BITBUCKET_COMMIT ?= $(shell git rev-parse --verify HEAD)
HASH ?= ${BITBUCKET_COMMIT}
STAGE := $(shell go run cli/deploy/deploy.go env ${BRANCH} 2> /dev/null)
PROTO_PATH := ${CWD}/lib/rpc

RUBY_CLIENT_LIB_PATH := ${CWD}/bin/ruby
RUBY_PROTO_OUT := ${RUBY_CLIENT_LIB_PATH}/lib/promotion

# coramil added
REPOSITORY_NAME ?= catalogservice
IMAGE_NAME ?= promotion
ENVIRONMENT ?= prod
DEPLOY_DOCKER_IMAGE_URL ?= catalogservice/promotion
DEPLOY_DOCKER_IMAGE_URL_WORKER ?= catalog/promotion-worker
.PHONY: docker-build-local docker-run-prod docker-run-dev docker-build-bitbucket

run:
	@go run cli/aloha.go server

run-worker:
	@go run cli/aloha.go worker
bindata:
ifndef BINDATA
	@go install github.com/jteeuwen/go-bindata/go-bindata@latest
endif
	@echo ${VERSION}-b${HASH} > resources/Version
	@go-bindata -pkg utils -o lib/utils/asset.go resources/sql/... resources/Version

gen-go:
ifndef PROTOC_GEN_GO
	@go install -v github.com/golang/protobuf/{proto,protoc-gen-go}
endif
	protoc --proto_path=${CWD}/lib/rpc/ --gofast_out=plugins=grpc:${CWD}/lib/rpc/ ${CWD}/lib/rpc/*.proto

build:
	@go build -race -v -o resources/deploy/${STAGE}/psrv cli/aloha.go
	@go build lib/rpc/client/client.go lib/rpc/client/conn.go

package:
	@cd resources/deploy/${STAGE} && zip -r ../artifact.zip .

deploy:
	@go run cli/deploy/deploy.go deploy resources/deploy/artifact.zip -e ${STAGE} -v ${VERSION}-b${BITBUCKET_COMMIT}

deploy-to-s3:
	@go run cli/deploy-to-s3/deploy-to-s3.go deploy-to-s3 resources/deploy/artifact.zip -e ${STAGE} -v ${VERSION}-b${BITBUCKET_COMMIT}

db-test:
	PSRV_ENV=test go test ./db/test -cover -coverpkg=./db -coverprofile=coverage.out -race -v
	@go tool cover -html=coverage.out

newrelic-deploy:
	@go run cli/aloha.go newrelic deploy -i ${NEWRELIC_ID} -k ${NEWRELIC_KEY} -r ${VERSION}-${HASH}

# coramil added

# for use on local environment development
docker-build-local:
ifeq ($(ENVIRONMENT),$(filter $(ENVIRONMENT), base dev test pre-prod prod prod-worker))
	docker build -t ${REPOSITORY_NAME}/${IMAGE_NAME}:prod-worker --target prod-worker --build-arg SSH_PRIVATE_KEY="$$(cat ~/.ssh/id_rsa)" .
	# docker build -t ${REPOSITORY_NAME}/${IMAGE_NAME}:${ENVIRONMENT} --target ${ENVIRONMENT} --build-arg SSH_PRIVATE_KEY="$$(cat ~/.ssh/id_rsa)" .
else
	@echo "Error: valid values for ENVIRONMENT is : base, dev, test, pre-prod, or prod"
endif

docker-run-prod:
	docker run --rm -it -p 9342:9342 ${REPOSITORY_NAME}/${IMAGE_NAME}:prod

# Automatically restarts the program when a change is detected in the directory.
docker-run-dev:
	docker run --rm -it -p 9342:9342 -v ${PWD}:/go/src/happyfresh.io/product-promotion ${REPOSITORY_NAME}/${IMAGE_NAME}:dev

docker-run-worker-dev:
	docker run --rm -it -v ${PWD}:/go/src/happyfresh.io/product-promotion ${REPOSITORY_NAME}/${IMAGE_NAME}:prod-worker
# for use with the bitbucket pipeline
docker-build-bitbucket:
	docker build --target prod -t ${DEPLOY_DOCKER_IMAGE_URL} . --build-arg SSH_PRIVATE_KEY="$$(cat /opt/atlassian/pipelines/agent/ssh/id_rsa)" --build-arg VERSION="$$(cat ./Version)"

gen-ruby:
	protoc --proto_path=${PROTO_PATH}/ \
	--ruby_out=${RUBY_PROTO_OUT} \
	--grpc_out=${RUBY_PROTO_OUT} \
	--plugin=protoc-gen-grpc=`which grpc_tools_ruby_protoc_plugin` \
	-I${GRPC_GATEWAY_ROOT}/third_party/googleapis \
	${PROTO_PATH}/promotion.proto
	sed -i.bak -E "s/(require.+gogoproto\/gogo.+)/# \1/g" ${RUBY_PROTO_OUT}/*.rb
	sed -i.bak -E "s/(require.+google\/api\/annotations.+)/# \1/g" ${RUBY_PROTO_OUT}/*.rb
	sed -i.bak -E "s/(require)(.+promotion.+)/require_relative\2/g" ${RUBY_PROTO_OUT}/promotion_services_pb.rb
	# sed -i.bak -E "s/(require_relative)(.+api_pb.+)/require_relative 'api'/g" ${RUBY_PROTO_OUT}/api_services_pb.rb
	rm -rf ${RUBY_PROTO_OUT}/*.bak

docker-promotion-worker-build-bitbucket:
	docker build --target prod-worker -t ${DEPLOY_DOCKER_IMAGE_URL_WORKER} . --build-arg SSH_PRIVATE_KEY="$$(cat /opt/atlassian/pipelines/agent/ssh/id_rsa)"

docker-build-ci:
	docker buildx create --use
	docker buildx build \
        --push \
        --platform=${TARGET_DOCKER_PLATFORM} \
        --target prod \
        --tag ${REGISTRY_IMAGE}:latest \
        --tag ${REGISTRY_IMAGE}:${IMAGE_TAG} \
        --cache-from ${REGISTRY_IMAGE}:latest \
        --build-arg VERSION="$$(cat ./Version)" \
        --build-arg CI_DEPLOY_TOKEN=${CI_DEPLOY_TOKEN} \
		--build-arg TARGET_DOCKER_PLATFORM=${TARGET_DOCKER_PLATFORM} \
        -f Dockerfile \
       .

docker-build-worker-ci:
	docker buildx create --use
	docker buildx build \
        --push \
        --platform=${TARGET_DOCKER_PLATFORM} \
        --target prod-worker \
        --tag ${REGISTRY_WORKER_IMAGE}:latest \
        --tag ${REGISTRY_WORKER_IMAGE}:${IMAGE_TAG} \
        --cache-from ${REGISTRY_WORKER_IMAGE}:latest \
        --build-arg VERSION="$$(cat ./Version)" \
        --build-arg CI_DEPLOY_TOKEN=${CI_DEPLOY_TOKEN} \
		--build-arg TARGET_DOCKER_PLATFORM=${TARGET_DOCKER_PLATFORM} \
        -f Dockerfile \
       .