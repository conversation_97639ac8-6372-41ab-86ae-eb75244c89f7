package worker

import (
	"context"
	"sync"

	"github.com/Shopify/sarama"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/lib/kafka"
)

// SubscriberTask :nodoc:
type SubscriberTask struct {
	consumerGroup sarama.ConsumerGroup
	consumer      *kafka.Consumer
	topics        []string
}

// NewSubscriberTask new subscriber task
func NewSubscriberTask(consumerGroup sarama.ConsumerGroup, topics []string, consumer *kafka.Consumer) Task {
	return &SubscriberTask{
		consumerGroup: consumerGroup,
		topics:        topics,
		consumer:      consumer,
	}
}

// Do run subscriber task
func (t *SubscriberTask) Do(ctx context.Context) error {
	wg := &sync.WaitGroup{}
	wg.Add(1)
	errChan := make(chan error)
	go consume(ctx, t.consumerGroup, t.topics, t.consumer, errChan)
	go func() {
		defer wg.Done()
		retryLimit := 100
		for {
			select {
			case err := <-errChan:
				log.For("Worker", "SubscriberTask").Error(err)
				if err == sarama.ErrTopicAuthorizationFailed ||
					err == sarama.ErrInvalidTopic {
					log.For("Worker", "SubscriberTask").Error(err)
				}

				if retryLimit == 0 {
					log.For("Worker", "SubscriberTask").Errorf("%v", "Retry limit reached")
					panic(0)
				}

				t.consumer.Ready = make(chan bool)
				go consume(ctx, t.consumerGroup, t.topics, t.consumer, errChan)
				log.For("Worker", "SubscribeTask").Infof("Consumer restarted for topics %v", t.topics)
				retryLimit--

			case <-ctx.Done():
				if err := t.consumerGroup.Close(); err != nil {
					log.For("Worker", "SubscriberTask").Error(err)
				}

				log.For("Worker", "SubscribeTask").Infof("Close Consumer %v", t.topics)
				return
			}
		}
	}()

	<-t.consumer.Ready
	log.For("Worker", "SubscribeTask").Infof("Consumer ready for topics %v", t.topics)
	wg.Wait()
	return nil
}

func consume(context context.Context, consumerGroup sarama.ConsumerGroup, topics []string, consumer *kafka.Consumer, errChan chan error) {
	for {
		if err := consumerGroup.Consume(context, topics, consumer); err != nil {
			errChan <- err
			return
		}

		if context.Err() != nil {
			errChan <- context.Err()
			return
		}

		consumer.Ready = make(chan bool)
	}
}
