package oteltracer

import (
	"context"
	"io/ioutil"
	"net/http"
	"time"

	sdktrace "go.opentelemetry.io/otel/sdk/trace"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	"happyfresh.io/product-promotion/config"
)

func getOtelCollectorAddress() (string, error) {
	if config.Env() == "production" || config.Env() == "staging" {
		req, err := http.NewRequest("GET", "http://***************/latest/meta-data/local-ipv4", nil)
		if err != nil {
			return "", err
		}

		cctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		req = req.WithContext(cctx)

		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			return "", err
		}

		b, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return "", err
		}

		host := string(b) + ":4317"
		return host, nil
	}
	return "localhost:4317", nil
}

func InitTracer() *sdktrace.TracerProvider {
	otelCollectorAddress, err := getOtelCollectorAddress()
	if err != nil {
		panic(err)
	}

	exporter, err := otlptrace.New(
		context.Background(),
		otlptracegrpc.NewClient(
			otlptracegrpc.WithInsecure(),
			otlptracegrpc.WithEndpoint(otelCollectorAddress),
		),
	)
	if err != nil {
		panic(err)
	}

	r, err := resource.New(
		context.Background(),
		resource.WithAttributes(
			attribute.String("service.name", "CatalogService-Promotion"),
			attribute.String("library.language", "go"),
		),
	)
	if err != nil {
		panic(err)
	}
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(r),
		sdktrace.WithSampler(sdktrace.AlwaysSample()),
	)
	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(propagation.TraceContext{}, propagation.Baggage{}))
	return tp
}
