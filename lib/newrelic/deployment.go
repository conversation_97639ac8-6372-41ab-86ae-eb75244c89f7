package newrelic

import (
	"bytes"
	"fmt"
	"net/http"
)

func CreateDeploymentMarker(appID, apiKey, revision string) error {
	url := fmt.Sprintf("https://api.newrelic.com/v2/applications/%s/deployments.json", appID)

	buf := &bytes.Buffer{}
	buf.WriteString(fmt.Sprintf(`{"deployment":{"revision":"%s"}}`, revision))

	r, err := http.NewRequest("POST", url, buf)
	if err != nil {
		return err
	}
	r.Header.Set("X-Api-Key", apiKey)
	r.Header.Set("Content-Type", "application/json")

	client := http.DefaultClient
	resp, err := client.Do(r)
	if err != nil {
		return err
	}
	if resp.StatusCode >= http.StatusBadRequest {
		err = fmt.Errorf("Recording deployment to New Relic failed, status code = %d", resp.StatusCode)
		return err
	}

	return nil
}
