package newrelic

import (
	newrelic "github.com/newrelic/go-agent"
)

func CreateConfig(appName, newrelicKey string) newrelic.Config {
	nrCfg := newrelic.NewConfig(appName, newrelicKey)

	nrCfg.CrossApplicationTracer.Enabled = false
	nrCfg.DistributedTracer.Enabled = true
	nrCfg.TransactionTracer.Threshold.IsApdexFailing = false

	return nrCfg
}

func CreateNewApplication(config newrelic.Config) (newrelic.Application, error) {
	return newrelic.NewApplication(config)
}
