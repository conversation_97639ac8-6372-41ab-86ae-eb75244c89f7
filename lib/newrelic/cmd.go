package newrelic

import (
	"errors"
	"fmt"

	"github.com/spf13/cobra"
)

var (
	Cmd = &cobra.Command{
		Use:   "newrelic",
		Short: "Run New Relic command",
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Usage()
		},
	}

	DeployCmd = &cobra.Command{
		Use:   "deploy",
		Short: "Record deployment command",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("Recording new deployment on New Relic")

			appID := cmd.PersistentFlags().Lookup("app-id").Value.String()
			if len(appID) <= 0 {
				err := errors.New("App ID cant be empty")
				return err
			}

			apiKey := cmd.PersistentFlags().Lookup("api-key").Value.String()
			if len(apiKey) <= 0 {
				err := errors.New("API key cant be empty")
				return err
			}

			rev := cmd.PersistentFlags().Lookup("rev").Value.String()
			if len(rev) <= 0 {
				err := errors.New("Revision cant be empty")
				return err
			}

			return CreateDeploymentMarker(appID, apiKey, rev)
		},
	}
)

func init() {
	DeployCmd.PersistentFlags().StringP("app-id", "i", "", "application ID")
	DeployCmd.PersistentFlags().StringP("api-key", "k", "", "application API Key")
	DeployCmd.PersistentFlags().StringP("rev", "r", "", "application revision/version")

	Cmd.AddCommand(DeployCmd)
}
