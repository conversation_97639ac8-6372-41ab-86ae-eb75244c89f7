package scram

import (
	"crypto/sha256"
	"hash"

	"github.com/xdg/scram"
)

// XDGSCRAMClient :nodoc:
type XDGSCRAMClient struct {
	*scram.Client
	*scram.ClientConversation
	scram.HashGeneratorFcn
}

// NewXDGSCRAMClient :nodoc:
func NewXDGSCRAMClient() *XDGSCRAMClient {
	return &XDGSCRAMClient{
		HashGeneratorFcn: func() hash.Hash {
			return sha256.New()
		},
	}
}

// Begin :nodoc:
func (x *XDGSCRAMClient) Begin(userName, password, authzID string) (err error) {
	x.Client, err = x.HashGeneratorFcn.NewClient(userName, password, authzID)
	if err != nil {
		return
	}
	x.ClientConversation = x.Client.NewConversation()
	return
}

// Step :nodoc
func (x *XDGSCRAMClient) Step(challenge string) (response string, err error) {
	response, err = x.ClientConversation.Step(challenge)
	return
}

// Done :nodoc:
func (x *XDGSCRAMClient) Done() bool {
	return x.ClientConversation.Done()
}
