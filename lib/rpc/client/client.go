package client

import (
	"context"
	"errors"
	"io"
	"strconv"

	"github.com/sony/gobreaker"
	"google.golang.org/grpc"
	"happyfresh.io/product-promotion/lib/rpc"
)

var (
	apiClient      rpc.ApiClient
	circuitBreaker *gobreaker.CircuitBreaker
)

type ErrTemporaryUnavailable error

type ErrTypeUnknown error

type Client interface {
	GetAllStockItemPromotions([]string, int, string, string) (*rpc.StockItemPromotionMultiResponse, error)
	GetAllStockItemPromotionChannel([]string, int, string) (*rpc.StockItemPromotionMultiResponse, error)
	GetPromotionDetailInfoWithFilter(*rpc.PromotionDetailInfoWithFilterRequest) (*rpc.PromotionDetailInfoWithFilterMultiResponse, error)
	GetPromotedProductCodes(int, string, string) ([]string, error)
	FindAllStartingPromotions([]string, []int) (*rpc.StockItemPromotionMultiResponse, error)
	StreamAllStartingPromotions([]string, []int) ([]*rpc.StockItemPromotionResponse, error)
	FindAllEndingPromotions([]string, []int) (*rpc.StockItemPromotionMultiResponse, error)
	StreamAllEndingPromotions([]string, []int) ([]*rpc.StockItemPromotionResponse, error)
	ImportAlohaPromotions(*rpc.ImportPromotionMultiRequest) ([]*rpc.ImportPromotionError, error)
	StreamImportPromotions([]*rpc.ImportPromotionDetailRequest) ([]*rpc.ImportPromotionError, error)
	DeletePromotions([]string) error
	DeletePromotionBasedOnSKU([]*rpc.DeletePromotionDetailsRequest) (*rpc.DeletePromotionDetailsResponse, error)
	GetPromotionChannel() (map[string]int64, error)
	GetPromotionSource() (*rpc.PromotionSourceResponse, error)
	GetPromotionDetailByPromotionCode(string) ([]*rpc.StockItemPromotionResponse, error)
	Ping() bool
	ImportPromotionQuota(*rpc.ImportPromotionQuotaRequestMultiple) (*rpc.ImportPromotionQuotaResponse, error)
	StreamImportPromotionQuota([]*rpc.ImportPromotionQuotaRequest) (*rpc.ImportPromotionQuotaResponse, error)
	GetPromotionQuotaDetailInfoWithFilter(*rpc.ImportPromotionQuotaRequest) (*rpc.PromotionQuotaDetailInfoWithFilterMultiResponse, error)
	GetExclusivePromotion(int, string, string) ([]string, error)
}

func NewClient(rpcClient ...rpc.ApiClient) Client {
	client := apiClient
	if rpcClient != nil && len(rpcClient) > 0 && rpcClient[0] != nil {
		client = rpcClient[0]
	}

	return &productPromotionClient{
		client: client,
	}
}

type productPromotionClient struct {
	client rpc.ApiClient
}

func (c *productPromotionClient) GetAllStockItemPromotions(productCodes []string, storeID int, channel, countryCode string) (*rpc.StockItemPromotionMultiResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		r := &rpc.StockItemPromotionMultiRequest{
			ProductCodes: productCodes,
			StoreIds:     []string{strconv.Itoa(storeID)},
			Channel:      channel,
			CountryCode:  countryCode,
		}
		return c.client.GetAllStockItemPromotion(context.Background(), r)
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.StockItemPromotionMultiResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetAllStockItemPromotionChannel(productCodes []string, storeID int, countryCode string) (*rpc.StockItemPromotionMultiResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		r := &rpc.StockItemPromotionMultiRequest{
			ProductCodes: productCodes,
			StoreIds:     []string{strconv.Itoa(storeID)},
			CountryCode:  countryCode,
		}
		return c.client.GetAllStockItemPromotionChannel(context.Background(), r)
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.StockItemPromotionMultiResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetPromotionChannel() (map[string]int64, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.GetPromotionChannel(context.Background(), &rpc.PromotionChannelRequest{})
		if err != nil {
			return nil, err
		}

		return res.PromotionChannelInfo, err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(map[string]int64)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetPromotionSource() (*rpc.PromotionSourceResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.GetPromotionSource(context.Background(), &rpc.PromotionSourceRequest{})
		if err != nil {
			return nil, err
		}
		return res, err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.PromotionSourceResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetPromotionDetailInfoWithFilter(p *rpc.PromotionDetailInfoWithFilterRequest) (*rpc.PromotionDetailInfoWithFilterMultiResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.GetPromotionDetailInfoWithFilter(context.Background(), p)
		if err != nil {
			return nil, err
		}
		return res, nil
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.PromotionDetailInfoWithFilterMultiResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) FindAllStartingPromotions(dates []string, storeIDs []int) (*rpc.StockItemPromotionMultiResponse, error) {
	return c.findAllPromotionsByExactDatesAndStoreIDs(dates, storeIDs, c.client.FindAllStartingPromotions)
}

func (c *productPromotionClient) StreamAllStartingPromotions(dates []string, storeIDs []int) ([]*rpc.StockItemPromotionResponse, error) {
	storeIdsInt64 := make([]int64, len(storeIDs))
	for i, v := range storeIDs {
		storeIdsInt64[i] = int64(v)
	}

	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		stream, err := c.client.StreamAllStartingPromotions(context.Background(), &rpc.DatesAndStoreIDs{
			Dates:    dates,
			StoreIds: storeIdsInt64,
		})
		if err != nil {
			return nil, err
		}

		stockItems := []*rpc.StockItemPromotionResponse{}
		for {
			stockItem, err := stream.Recv()
			if err == io.EOF {
				break
			}

			if err != nil {
				continue
			}

			stockItems = append(stockItems, stockItem)
		}

		return stockItems, nil
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.([]*rpc.StockItemPromotionResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetPromotedProductCodes(storeID int, date, channel string) ([]string, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.GetPromotedProductCodes(context.Background(), &rpc.StoreIDDateChannel{
			StoreId: int64(storeID),
			Date:    date,
			Channel: channel,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.ProductCodes)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result.ProductCodes, nil
}

func (c *productPromotionClient) FindAllEndingPromotions(dates []string, storeIDs []int) (*rpc.StockItemPromotionMultiResponse, error) {
	return c.findAllPromotionsByExactDatesAndStoreIDs(dates, storeIDs, c.client.FindAllEndingPromotions)
}

func (c *productPromotionClient) StreamAllEndingPromotions(dates []string, storeIDs []int) ([]*rpc.StockItemPromotionResponse, error) {
	storeIdsInt64 := make([]int64, len(storeIDs))
	for i, v := range storeIDs {
		storeIdsInt64[i] = int64(v)
	}

	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		stream, err := c.client.StreamAllEndingPromotions(context.Background(), &rpc.DatesAndStoreIDs{
			Dates:    dates,
			StoreIds: storeIdsInt64,
		})
		if err != nil {
			return nil, err
		}

		stockItems := []*rpc.StockItemPromotionResponse{}
		for {
			stockItem, err := stream.Recv()
			if err == io.EOF {
				break
			}

			if err != nil {
				continue
			}

			stockItems = append(stockItems, stockItem)
		}

		return stockItems, nil
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.([]*rpc.StockItemPromotionResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) ImportAlohaPromotions(p *rpc.ImportPromotionMultiRequest) ([]*rpc.ImportPromotionError, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.ImportPromotion(context.Background(), p)
		if err != nil {
			return nil, err
		}
		return res.ImportPromotionError, err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.([]*rpc.ImportPromotionError)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) StreamImportPromotions(p []*rpc.ImportPromotionDetailRequest) ([]*rpc.ImportPromotionError, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		stream, _ := c.client.StreamImportPromotion(context.Background())
		for _, promo := range p {
			err := stream.Send(promo)
			if err != nil {
				return nil, err
			}
		}

		result, err := stream.CloseAndRecv()
		if err != nil {
			return nil, err
		}

		return result.GetImportPromotionError(), err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.([]*rpc.ImportPromotionError)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) DeletePromotions(promotionCodes []string) error {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		ok, err := c.client.DeletePromotions(context.Background(), &rpc.PromotionCodes{PromotionCodes: promotionCodes})
		return ok.GetOk() && err == nil, err
	})
	if err != nil {
		return err
	}

	ok, _ := r.(bool)
	if !ok {
		return ErrTypeUnknown(errors.New("Promotion not found"))
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return ErrTemporaryUnavailable(err)
	}

	return nil
}

func (c *productPromotionClient) DeletePromotionBasedOnSKU(p []*rpc.DeletePromotionDetailsRequest) (*rpc.DeletePromotionDetailsResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		stream, _ := c.client.DeletePromotionBasedOnSKU(context.Background())
		for _, promo := range p {
			err := stream.Send(promo)
			if err != nil {
				return nil, err
			}
		}

		result, err := stream.CloseAndRecv()
		if err != nil {
			return nil, err
		}

		return result, err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.DeletePromotionDetailsResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetPromotionDetailByPromotionCode(promotionCode string) ([]*rpc.StockItemPromotionResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		stream, err := c.client.GetPromotionDetailByPromotionCode(context.Background(), &rpc.PromotionDetailByPromotionCodeRequest{
			PromotionCode: promotionCode,
		})
		if err != nil {
			return nil, err
		}

		promotionDetails := []*rpc.StockItemPromotionResponse{}
		for {
			promotionDetail, err := stream.Recv()
			if err == io.EOF {
				break
			}

			if err != nil {
				continue
			}

			promotionDetails = append(promotionDetails, promotionDetail)
		}

		return promotionDetails, nil
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.([]*rpc.StockItemPromotionResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) Ping() bool {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		ok, err := c.client.Ping(context.Background(), &rpc.Null{})
		return ok.GetOk() && err == nil, err
	})
	if err != nil {
		return false
	}

	result, ok := r.(bool)
	if !ok {
		return false
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return false
	}

	return result
}

func (c *productPromotionClient) findAllPromotionsByExactDatesAndStoreIDs(dates []string, storeIDs []int, f func(context.Context, *rpc.DatesAndStoreIDs, ...grpc.CallOption) (*rpc.StockItemPromotionMultiResponse, error)) (*rpc.StockItemPromotionMultiResponse, error) {
	storeIdsInt64 := make([]int64, len(storeIDs))
	for i, v := range storeIDs {
		storeIdsInt64[i] = int64(v)
	}
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		r := &rpc.DatesAndStoreIDs{
			Dates:    dates,
			StoreIds: storeIdsInt64,
		}
		res, err := f(context.Background(), r)
		if err != nil {
			return nil, err
		}
		return res, err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.StockItemPromotionMultiResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) ImportPromotionQuota(p *rpc.ImportPromotionQuotaRequestMultiple) (*rpc.ImportPromotionQuotaResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.ImportPromotionQuota(context.Background(), p)
		if err != nil {
			return nil, err
		}
		return res, err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.ImportPromotionQuotaResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) StreamImportPromotionQuota(p []*rpc.ImportPromotionQuotaRequest) (*rpc.ImportPromotionQuotaResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		stream, _ := c.client.StreamImportPromotionQuota(context.Background())
		for _, promo := range p {
			err := stream.Send(promo)
			if err != nil {
				return nil, err
			}
		}

		result, err := stream.CloseAndRecv()
		if err != nil {
			return nil, err
		}

		return result, err
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.ImportPromotionQuotaResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetPromotionQuotaDetailInfoWithFilter(p *rpc.ImportPromotionQuotaRequest) (*rpc.PromotionQuotaDetailInfoWithFilterMultiResponse, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.GetPromotionQuotaDetailInfoWithFilter(context.Background(), p)
		return res, err
	})

	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.PromotionQuotaDetailInfoWithFilterMultiResponse)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result, nil
}

func (c *productPromotionClient) GetExclusivePromotion(storeID int, countryCode string, channel string) ([]string, error) {
	r, err := circuitBreaker.Execute(func() (interface{}, error) {
		res, err := c.client.GetExclusivePromotion(context.Background(), &rpc.ExclusivePromoRequest{
			StoreId:     int64(storeID),
			CountryCode: countryCode,
			Channel:     channel,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	})
	if err != nil {
		return nil, err
	}

	result, ok := r.(*rpc.ProductCodes)
	if !ok {
		return nil, ErrTypeUnknown(err)
	}

	if circuitBreaker.State() != gobreaker.StateClosed {
		return nil, ErrTemporaryUnavailable(err)
	}

	return result.ProductCodes, nil
}
