package client

import (
	"crypto/tls"
	"time"

	"github.com/sony/gobreaker"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/lib/oteltracer"
	"happyfresh.io/product-promotion/lib/rpc"
)

type CircuitBreakerOption struct {
	Timeout                      time.Duration
	Interval                     time.Duration
	ConsecutiveFailuresThreshold int
	RPM                          int
	FailureRatioThreshold        float64
}

func NewConnection(address string, circuitBreakerOption *CircuitBreakerOption) error {
	conn, err := grpc.Dial(
		address,
		grpc.WithUnaryInterceptor(otelgrpc.UnaryClientInterceptor()),
		grpc.WithInsecure(),
		grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy":"round_robin"}`),
	)
	if err != nil {
		log.For("client", "NewConnection").Fatalf("Fail to dial: %v", err)
		return err
	}

	apiClient = rpc.NewApiClient(conn)

	circuitBreaker = gobreaker.NewCircuitBreaker(gobreaker.Settings{
		Name:     "PSRV-Client",
		Timeout:  circuitBreakerOption.Timeout,
		Interval: circuitBreakerOption.Interval,
		ReadyToTrip: func(c gobreaker.Counts) bool {
			if c.ConsecutiveFailures >= uint32(circuitBreakerOption.ConsecutiveFailuresThreshold) {
				return true
			}

			failureRatio := float64(c.TotalFailures) / float64(c.Requests)
			if int(c.Requests) >= circuitBreakerOption.RPM && failureRatio > circuitBreakerOption.FailureRatioThreshold {
				return true
			}

			return false
		},
	})

	return nil
}

func NewConnectionSecure(address string, circuitBreakerOption *CircuitBreakerOption) error {
	secureOpt := grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{}))
	conn, err := grpc.Dial(
		address,
		secureOpt,
		grpc.WithUnaryInterceptor(
			oteltracer.ChainUnaryClientInterceptor(true, true),
		),
		grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy":"round_robin"}`),
	)
	if err != nil {
		log.For("client", "NewConnection").Fatalf("Fail to dial: %v", err)
		return err
	}

	apiClient = rpc.NewApiClient(conn)

	circuitBreaker = gobreaker.NewCircuitBreaker(gobreaker.Settings{
		Name:     "PSRV-Client",
		Timeout:  circuitBreakerOption.Timeout,
		Interval: circuitBreakerOption.Interval,
		ReadyToTrip: func(c gobreaker.Counts) bool {
			if c.ConsecutiveFailures >= uint32(circuitBreakerOption.ConsecutiveFailuresThreshold) {
				return true
			}

			failureRatio := float64(c.TotalFailures) / float64(c.Requests)
			if int(c.Requests) >= circuitBreakerOption.RPM && failureRatio > circuitBreakerOption.FailureRatioThreshold {
				return true
			}

			return false
		},
	})

	return nil
}
