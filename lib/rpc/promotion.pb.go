// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: promotion.proto

package rpc

import (
	context "context"
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PromotionQuotaDetailInfoWithFilterMultiResponse struct {
	PromotionQuotaDetailInfo []*PromotionQuotaDetailInfoWithFilterResponse `protobuf:"bytes,1,rep,name=promotion_quota_detail_info,json=promotionQuotaDetailInfo,proto3" json:"promotion_quota_detail_info,omitempty"`
	Count                    int64                                         `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                                      `json:"-"`
	XXX_unrecognized         []byte                                        `json:"-"`
	XXX_sizecache            int32                                         `json:"-"`
}

func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) Reset() {
	*m = PromotionQuotaDetailInfoWithFilterMultiResponse{}
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) String() string {
	return proto.CompactTextString(m)
}
func (*PromotionQuotaDetailInfoWithFilterMultiResponse) ProtoMessage() {}
func (*PromotionQuotaDetailInfoWithFilterMultiResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{0}
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionQuotaDetailInfoWithFilterMultiResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionQuotaDetailInfoWithFilterMultiResponse.Merge(m, src)
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) XXX_Size() int {
	return m.Size()
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionQuotaDetailInfoWithFilterMultiResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionQuotaDetailInfoWithFilterMultiResponse proto.InternalMessageInfo

func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) GetPromotionQuotaDetailInfo() []*PromotionQuotaDetailInfoWithFilterResponse {
	if m != nil {
		return m.PromotionQuotaDetailInfo
	}
	return nil
}

func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type PromotionQuotaDetailInfoWithFilterResponse struct {
	PromotionCode        string   `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	ProductCode          string   `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	Quota                int32    `protobuf:"varint,3,opt,name=quota,proto3" json:"quota,omitempty"`
	Active               bool     `protobuf:"varint,4,opt,name=active,proto3" json:"active,omitempty"`
	CreatedAt            string   `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            string   `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) Reset() {
	*m = PromotionQuotaDetailInfoWithFilterResponse{}
}
func (m *PromotionQuotaDetailInfoWithFilterResponse) String() string {
	return proto.CompactTextString(m)
}
func (*PromotionQuotaDetailInfoWithFilterResponse) ProtoMessage() {}
func (*PromotionQuotaDetailInfoWithFilterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{1}
}
func (m *PromotionQuotaDetailInfoWithFilterResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionQuotaDetailInfoWithFilterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionQuotaDetailInfoWithFilterResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionQuotaDetailInfoWithFilterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionQuotaDetailInfoWithFilterResponse.Merge(m, src)
}
func (m *PromotionQuotaDetailInfoWithFilterResponse) XXX_Size() int {
	return m.Size()
}
func (m *PromotionQuotaDetailInfoWithFilterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionQuotaDetailInfoWithFilterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionQuotaDetailInfoWithFilterResponse proto.InternalMessageInfo

func (m *PromotionQuotaDetailInfoWithFilterResponse) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) GetQuota() int32 {
	if m != nil {
		return m.Quota
	}
	return 0
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) GetActive() bool {
	if m != nil {
		return m.Active
	}
	return false
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

type ImportPromotionQuotaRequest struct {
	PromotionCode        string   `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	ProductCode          string   `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	Quota                int32    `protobuf:"varint,3,opt,name=quota,proto3" json:"quota,omitempty"`
	Active               bool     `protobuf:"varint,4,opt,name=active,proto3" json:"active,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImportPromotionQuotaRequest) Reset()         { *m = ImportPromotionQuotaRequest{} }
func (m *ImportPromotionQuotaRequest) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionQuotaRequest) ProtoMessage()    {}
func (*ImportPromotionQuotaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{2}
}
func (m *ImportPromotionQuotaRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionQuotaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionQuotaRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionQuotaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionQuotaRequest.Merge(m, src)
}
func (m *ImportPromotionQuotaRequest) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionQuotaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionQuotaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionQuotaRequest proto.InternalMessageInfo

func (m *ImportPromotionQuotaRequest) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *ImportPromotionQuotaRequest) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *ImportPromotionQuotaRequest) GetQuota() int32 {
	if m != nil {
		return m.Quota
	}
	return 0
}

func (m *ImportPromotionQuotaRequest) GetActive() bool {
	if m != nil {
		return m.Active
	}
	return false
}

type ImportPromotionQuotaRequestMultiple struct {
	ImportPromotionQuotaRequest []*ImportPromotionQuotaRequest `protobuf:"bytes,1,rep,name=ImportPromotionQuotaRequest,proto3" json:"ImportPromotionQuotaRequest,omitempty"`
	XXX_NoUnkeyedLiteral        struct{}                       `json:"-"`
	XXX_unrecognized            []byte                         `json:"-"`
	XXX_sizecache               int32                          `json:"-"`
}

func (m *ImportPromotionQuotaRequestMultiple) Reset()         { *m = ImportPromotionQuotaRequestMultiple{} }
func (m *ImportPromotionQuotaRequestMultiple) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionQuotaRequestMultiple) ProtoMessage()    {}
func (*ImportPromotionQuotaRequestMultiple) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{3}
}
func (m *ImportPromotionQuotaRequestMultiple) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionQuotaRequestMultiple) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionQuotaRequestMultiple.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionQuotaRequestMultiple) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionQuotaRequestMultiple.Merge(m, src)
}
func (m *ImportPromotionQuotaRequestMultiple) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionQuotaRequestMultiple) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionQuotaRequestMultiple.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionQuotaRequestMultiple proto.InternalMessageInfo

func (m *ImportPromotionQuotaRequestMultiple) GetImportPromotionQuotaRequest() []*ImportPromotionQuotaRequest {
	if m != nil {
		return m.ImportPromotionQuotaRequest
	}
	return nil
}

type ImportPromotionQuotaResponse struct {
	ImportPromotionQuotaError []*ImportPromotionQuotaError `protobuf:"bytes,1,rep,name=importPromotionQuotaError,proto3" json:"importPromotionQuotaError,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                     `json:"-"`
	XXX_unrecognized          []byte                       `json:"-"`
	XXX_sizecache             int32                        `json:"-"`
}

func (m *ImportPromotionQuotaResponse) Reset()         { *m = ImportPromotionQuotaResponse{} }
func (m *ImportPromotionQuotaResponse) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionQuotaResponse) ProtoMessage()    {}
func (*ImportPromotionQuotaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{4}
}
func (m *ImportPromotionQuotaResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionQuotaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionQuotaResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionQuotaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionQuotaResponse.Merge(m, src)
}
func (m *ImportPromotionQuotaResponse) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionQuotaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionQuotaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionQuotaResponse proto.InternalMessageInfo

func (m *ImportPromotionQuotaResponse) GetImportPromotionQuotaError() []*ImportPromotionQuotaError {
	if m != nil {
		return m.ImportPromotionQuotaError
	}
	return nil
}

type ImportPromotionQuotaError struct {
	PromotionCode        string   `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	ProductCode          string   `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	Errors               []string `protobuf:"bytes,3,rep,name=errors,proto3" json:"errors,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImportPromotionQuotaError) Reset()         { *m = ImportPromotionQuotaError{} }
func (m *ImportPromotionQuotaError) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionQuotaError) ProtoMessage()    {}
func (*ImportPromotionQuotaError) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{5}
}
func (m *ImportPromotionQuotaError) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionQuotaError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionQuotaError.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionQuotaError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionQuotaError.Merge(m, src)
}
func (m *ImportPromotionQuotaError) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionQuotaError) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionQuotaError.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionQuotaError proto.InternalMessageInfo

func (m *ImportPromotionQuotaError) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *ImportPromotionQuotaError) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *ImportPromotionQuotaError) GetErrors() []string {
	if m != nil {
		return m.Errors
	}
	return nil
}

type ImportPromotionDetail struct {
	ProductCode          string            `protobuf:"bytes,1,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	StoreId              int64             `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	ChannelId            int64             `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Type                 string            `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Rules                map[string]string `protobuf:"bytes,5,rep,name=rules,proto3" json:"rules,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Actions              map[string]string `protobuf:"bytes,6,rep,name=actions,proto3" json:"actions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ImportPromotionDetail) Reset()         { *m = ImportPromotionDetail{} }
func (m *ImportPromotionDetail) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionDetail) ProtoMessage()    {}
func (*ImportPromotionDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{6}
}
func (m *ImportPromotionDetail) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionDetail.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionDetail.Merge(m, src)
}
func (m *ImportPromotionDetail) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionDetail proto.InternalMessageInfo

func (m *ImportPromotionDetail) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *ImportPromotionDetail) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *ImportPromotionDetail) GetChannelId() int64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ImportPromotionDetail) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *ImportPromotionDetail) GetRules() map[string]string {
	if m != nil {
		return m.Rules
	}
	return nil
}

func (m *ImportPromotionDetail) GetActions() map[string]string {
	if m != nil {
		return m.Actions
	}
	return nil
}

type ImportPromotionDetailRequest struct {
	PromotionCode        string            `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	SourceId             int64             `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	StartDate            string            `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	LastDate             string            `protobuf:"bytes,4,opt,name=last_date,json=lastDate,proto3" json:"last_date,omitempty"`
	ProductCode          string            `protobuf:"bytes,5,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	StoreId              int64             `protobuf:"varint,6,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	ChannelId            int64             `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Type                 string            `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`
	Rules                map[string]string `protobuf:"bytes,9,rep,name=rules,proto3" json:"rules,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Actions              map[string]string `protobuf:"bytes,10,rep,name=actions,proto3" json:"actions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ImportPromotionDetailRequest) Reset()         { *m = ImportPromotionDetailRequest{} }
func (m *ImportPromotionDetailRequest) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionDetailRequest) ProtoMessage()    {}
func (*ImportPromotionDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{7}
}
func (m *ImportPromotionDetailRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionDetailRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionDetailRequest.Merge(m, src)
}
func (m *ImportPromotionDetailRequest) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionDetailRequest proto.InternalMessageInfo

func (m *ImportPromotionDetailRequest) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *ImportPromotionDetailRequest) GetSourceId() int64 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *ImportPromotionDetailRequest) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *ImportPromotionDetailRequest) GetLastDate() string {
	if m != nil {
		return m.LastDate
	}
	return ""
}

func (m *ImportPromotionDetailRequest) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *ImportPromotionDetailRequest) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *ImportPromotionDetailRequest) GetChannelId() int64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ImportPromotionDetailRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *ImportPromotionDetailRequest) GetRules() map[string]string {
	if m != nil {
		return m.Rules
	}
	return nil
}

func (m *ImportPromotionDetailRequest) GetActions() map[string]string {
	if m != nil {
		return m.Actions
	}
	return nil
}

type PromotionChannelRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionChannelRequest) Reset()         { *m = PromotionChannelRequest{} }
func (m *PromotionChannelRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionChannelRequest) ProtoMessage()    {}
func (*PromotionChannelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{8}
}
func (m *PromotionChannelRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionChannelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionChannelRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionChannelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionChannelRequest.Merge(m, src)
}
func (m *PromotionChannelRequest) XXX_Size() int {
	return m.Size()
}
func (m *PromotionChannelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionChannelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionChannelRequest proto.InternalMessageInfo

type PromotionChannelResponse struct {
	PromotionChannelInfo map[string]int64 `protobuf:"bytes,1,rep,name=PromotionChannelInfo,proto3" json:"PromotionChannelInfo,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PromotionChannelResponse) Reset()         { *m = PromotionChannelResponse{} }
func (m *PromotionChannelResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionChannelResponse) ProtoMessage()    {}
func (*PromotionChannelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{9}
}
func (m *PromotionChannelResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionChannelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionChannelResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionChannelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionChannelResponse.Merge(m, src)
}
func (m *PromotionChannelResponse) XXX_Size() int {
	return m.Size()
}
func (m *PromotionChannelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionChannelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionChannelResponse proto.InternalMessageInfo

func (m *PromotionChannelResponse) GetPromotionChannelInfo() map[string]int64 {
	if m != nil {
		return m.PromotionChannelInfo
	}
	return nil
}

type StockItemPromotionMultiRequest struct {
	ProductCodes         []string `protobuf:"bytes,1,rep,name=product_codes,json=productCodes,proto3" json:"product_codes,omitempty"`
	StoreIds             []string `protobuf:"bytes,2,rep,name=store_ids,json=storeIds,proto3" json:"store_ids,omitempty"`
	Channel              string   `protobuf:"bytes,3,opt,name=channel,proto3" json:"channel,omitempty"`
	CountryCode          string   `protobuf:"bytes,4,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StockItemPromotionMultiRequest) Reset()         { *m = StockItemPromotionMultiRequest{} }
func (m *StockItemPromotionMultiRequest) String() string { return proto.CompactTextString(m) }
func (*StockItemPromotionMultiRequest) ProtoMessage()    {}
func (*StockItemPromotionMultiRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{10}
}
func (m *StockItemPromotionMultiRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StockItemPromotionMultiRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StockItemPromotionMultiRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StockItemPromotionMultiRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StockItemPromotionMultiRequest.Merge(m, src)
}
func (m *StockItemPromotionMultiRequest) XXX_Size() int {
	return m.Size()
}
func (m *StockItemPromotionMultiRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StockItemPromotionMultiRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StockItemPromotionMultiRequest proto.InternalMessageInfo

func (m *StockItemPromotionMultiRequest) GetProductCodes() []string {
	if m != nil {
		return m.ProductCodes
	}
	return nil
}

func (m *StockItemPromotionMultiRequest) GetStoreIds() []string {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *StockItemPromotionMultiRequest) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *StockItemPromotionMultiRequest) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

type StockItemPromotionResponse struct {
	PromotionCode        string            `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	StartDate            string            `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	LastDate             string            `protobuf:"bytes,3,opt,name=last_date,json=lastDate,proto3" json:"last_date,omitempty"`
	ProductCode          string            `protobuf:"bytes,4,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	StoreId              int64             `protobuf:"varint,5,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Type                 string            `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Channel              string            `protobuf:"bytes,7,opt,name=channel,proto3" json:"channel,omitempty"`
	Rules                map[string]string `protobuf:"bytes,8,rep,name=rules,proto3" json:"rules,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Actions              map[string]string `protobuf:"bytes,9,rep,name=actions,proto3" json:"actions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DeletedAt            string            `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	SourceId             int64             `protobuf:"varint,11,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *StockItemPromotionResponse) Reset()         { *m = StockItemPromotionResponse{} }
func (m *StockItemPromotionResponse) String() string { return proto.CompactTextString(m) }
func (*StockItemPromotionResponse) ProtoMessage()    {}
func (*StockItemPromotionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{11}
}
func (m *StockItemPromotionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StockItemPromotionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StockItemPromotionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StockItemPromotionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StockItemPromotionResponse.Merge(m, src)
}
func (m *StockItemPromotionResponse) XXX_Size() int {
	return m.Size()
}
func (m *StockItemPromotionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StockItemPromotionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StockItemPromotionResponse proto.InternalMessageInfo

func (m *StockItemPromotionResponse) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *StockItemPromotionResponse) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *StockItemPromotionResponse) GetLastDate() string {
	if m != nil {
		return m.LastDate
	}
	return ""
}

func (m *StockItemPromotionResponse) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *StockItemPromotionResponse) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *StockItemPromotionResponse) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *StockItemPromotionResponse) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *StockItemPromotionResponse) GetRules() map[string]string {
	if m != nil {
		return m.Rules
	}
	return nil
}

func (m *StockItemPromotionResponse) GetActions() map[string]string {
	if m != nil {
		return m.Actions
	}
	return nil
}

func (m *StockItemPromotionResponse) GetDeletedAt() string {
	if m != nil {
		return m.DeletedAt
	}
	return ""
}

func (m *StockItemPromotionResponse) GetSourceId() int64 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

type StockItemPromotionMultiResponse struct {
	StockItemPromotions  []*StockItemPromotionResponse `protobuf:"bytes,1,rep,name=stock_item_promotions,json=stockItemPromotions,proto3" json:"stock_item_promotions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *StockItemPromotionMultiResponse) Reset()         { *m = StockItemPromotionMultiResponse{} }
func (m *StockItemPromotionMultiResponse) String() string { return proto.CompactTextString(m) }
func (*StockItemPromotionMultiResponse) ProtoMessage()    {}
func (*StockItemPromotionMultiResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{12}
}
func (m *StockItemPromotionMultiResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StockItemPromotionMultiResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StockItemPromotionMultiResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StockItemPromotionMultiResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StockItemPromotionMultiResponse.Merge(m, src)
}
func (m *StockItemPromotionMultiResponse) XXX_Size() int {
	return m.Size()
}
func (m *StockItemPromotionMultiResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StockItemPromotionMultiResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StockItemPromotionMultiResponse proto.InternalMessageInfo

func (m *StockItemPromotionMultiResponse) GetStockItemPromotions() []*StockItemPromotionResponse {
	if m != nil {
		return m.StockItemPromotions
	}
	return nil
}

type ProductCodes struct {
	ProductCodes         []string `protobuf:"bytes,1,rep,name=product_codes,json=productCodes,proto3" json:"product_codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProductCodes) Reset()         { *m = ProductCodes{} }
func (m *ProductCodes) String() string { return proto.CompactTextString(m) }
func (*ProductCodes) ProtoMessage()    {}
func (*ProductCodes) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{13}
}
func (m *ProductCodes) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ProductCodes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ProductCodes.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ProductCodes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductCodes.Merge(m, src)
}
func (m *ProductCodes) XXX_Size() int {
	return m.Size()
}
func (m *ProductCodes) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductCodes.DiscardUnknown(m)
}

var xxx_messageInfo_ProductCodes proto.InternalMessageInfo

func (m *ProductCodes) GetProductCodes() []string {
	if m != nil {
		return m.ProductCodes
	}
	return nil
}

type ImportPromotionRequest struct {
	PromotionCode        string                   `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	SourceId             int64                    `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	PromotionDetails     []*ImportPromotionDetail `protobuf:"bytes,3,rep,name=promotion_details,json=promotionDetails,proto3" json:"promotion_details,omitempty"`
	StartDate            string                   `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	LastDate             string                   `protobuf:"bytes,5,opt,name=last_date,json=lastDate,proto3" json:"last_date,omitempty"`
	CreatedAt            string                   `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            string                   `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ImportPromotionRequest) Reset()         { *m = ImportPromotionRequest{} }
func (m *ImportPromotionRequest) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionRequest) ProtoMessage()    {}
func (*ImportPromotionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{14}
}
func (m *ImportPromotionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionRequest.Merge(m, src)
}
func (m *ImportPromotionRequest) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionRequest proto.InternalMessageInfo

func (m *ImportPromotionRequest) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *ImportPromotionRequest) GetSourceId() int64 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *ImportPromotionRequest) GetPromotionDetails() []*ImportPromotionDetail {
	if m != nil {
		return m.PromotionDetails
	}
	return nil
}

func (m *ImportPromotionRequest) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *ImportPromotionRequest) GetLastDate() string {
	if m != nil {
		return m.LastDate
	}
	return ""
}

func (m *ImportPromotionRequest) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *ImportPromotionRequest) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

type ImportPromotionMultiRequest struct {
	ImportPromotionRequest []*ImportPromotionRequest `protobuf:"bytes,1,rep,name=import_promotion_request,json=importPromotionRequest,proto3" json:"import_promotion_request,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                  `json:"-"`
	XXX_unrecognized       []byte                    `json:"-"`
	XXX_sizecache          int32                     `json:"-"`
}

func (m *ImportPromotionMultiRequest) Reset()         { *m = ImportPromotionMultiRequest{} }
func (m *ImportPromotionMultiRequest) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionMultiRequest) ProtoMessage()    {}
func (*ImportPromotionMultiRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{15}
}
func (m *ImportPromotionMultiRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionMultiRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionMultiRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionMultiRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionMultiRequest.Merge(m, src)
}
func (m *ImportPromotionMultiRequest) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionMultiRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionMultiRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionMultiRequest proto.InternalMessageInfo

func (m *ImportPromotionMultiRequest) GetImportPromotionRequest() []*ImportPromotionRequest {
	if m != nil {
		return m.ImportPromotionRequest
	}
	return nil
}

type ImportPromotionResponse struct {
	ImportPromotionError []*ImportPromotionError `protobuf:"bytes,1,rep,name=importPromotionError,proto3" json:"importPromotionError,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ImportPromotionResponse) Reset()         { *m = ImportPromotionResponse{} }
func (m *ImportPromotionResponse) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionResponse) ProtoMessage()    {}
func (*ImportPromotionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{16}
}
func (m *ImportPromotionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionResponse.Merge(m, src)
}
func (m *ImportPromotionResponse) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionResponse proto.InternalMessageInfo

func (m *ImportPromotionResponse) GetImportPromotionError() []*ImportPromotionError {
	if m != nil {
		return m.ImportPromotionError
	}
	return nil
}

type ImportPromotionError struct {
	PromotionCode        string   `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	ProductCode          string   `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	StoreId              int64    `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	ChannelId            int64    `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Errors               []string `protobuf:"bytes,5,rep,name=errors,proto3" json:"errors,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImportPromotionError) Reset()         { *m = ImportPromotionError{} }
func (m *ImportPromotionError) String() string { return proto.CompactTextString(m) }
func (*ImportPromotionError) ProtoMessage()    {}
func (*ImportPromotionError) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{17}
}
func (m *ImportPromotionError) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportPromotionError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportPromotionError.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportPromotionError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportPromotionError.Merge(m, src)
}
func (m *ImportPromotionError) XXX_Size() int {
	return m.Size()
}
func (m *ImportPromotionError) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportPromotionError.DiscardUnknown(m)
}

var xxx_messageInfo_ImportPromotionError proto.InternalMessageInfo

func (m *ImportPromotionError) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *ImportPromotionError) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *ImportPromotionError) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *ImportPromotionError) GetChannelId() int64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ImportPromotionError) GetErrors() []string {
	if m != nil {
		return m.Errors
	}
	return nil
}

type PromotionSource struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionSource) Reset()         { *m = PromotionSource{} }
func (m *PromotionSource) String() string { return proto.CompactTextString(m) }
func (*PromotionSource) ProtoMessage()    {}
func (*PromotionSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{18}
}
func (m *PromotionSource) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionSource.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSource.Merge(m, src)
}
func (m *PromotionSource) XXX_Size() int {
	return m.Size()
}
func (m *PromotionSource) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSource.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSource proto.InternalMessageInfo

func (m *PromotionSource) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PromotionSource) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type PromotionSourceRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionSourceRequest) Reset()         { *m = PromotionSourceRequest{} }
func (m *PromotionSourceRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionSourceRequest) ProtoMessage()    {}
func (*PromotionSourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{19}
}
func (m *PromotionSourceRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionSourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionSourceRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionSourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSourceRequest.Merge(m, src)
}
func (m *PromotionSourceRequest) XXX_Size() int {
	return m.Size()
}
func (m *PromotionSourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSourceRequest proto.InternalMessageInfo

type PromotionSourceResponse struct {
	PromotionSource      []*PromotionSource `protobuf:"bytes,1,rep,name=promotion_source,json=promotionSource,proto3" json:"promotion_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PromotionSourceResponse) Reset()         { *m = PromotionSourceResponse{} }
func (m *PromotionSourceResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionSourceResponse) ProtoMessage()    {}
func (*PromotionSourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{20}
}
func (m *PromotionSourceResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionSourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionSourceResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionSourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionSourceResponse.Merge(m, src)
}
func (m *PromotionSourceResponse) XXX_Size() int {
	return m.Size()
}
func (m *PromotionSourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionSourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionSourceResponse proto.InternalMessageInfo

func (m *PromotionSourceResponse) GetPromotionSource() []*PromotionSource {
	if m != nil {
		return m.PromotionSource
	}
	return nil
}

type PromotionDetailInfoWithFilterRequest struct {
	StoreIds             []int64  `protobuf:"varint,1,rep,packed,name=store_ids,json=storeIds,proto3" json:"store_ids,omitempty"`
	ProductCode          string   `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	PromotionCode        string   `protobuf:"bytes,3,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	StartDate            string   `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	LastDate             string   `protobuf:"bytes,5,opt,name=last_date,json=lastDate,proto3" json:"last_date,omitempty"`
	SourceId             int64    `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Size_                int64    `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`
	From                 int64    `protobuf:"varint,8,opt,name=from,proto3" json:"from,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionDetailInfoWithFilterRequest) Reset()         { *m = PromotionDetailInfoWithFilterRequest{} }
func (m *PromotionDetailInfoWithFilterRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionDetailInfoWithFilterRequest) ProtoMessage()    {}
func (*PromotionDetailInfoWithFilterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{21}
}
func (m *PromotionDetailInfoWithFilterRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionDetailInfoWithFilterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionDetailInfoWithFilterRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionDetailInfoWithFilterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionDetailInfoWithFilterRequest.Merge(m, src)
}
func (m *PromotionDetailInfoWithFilterRequest) XXX_Size() int {
	return m.Size()
}
func (m *PromotionDetailInfoWithFilterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionDetailInfoWithFilterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionDetailInfoWithFilterRequest proto.InternalMessageInfo

func (m *PromotionDetailInfoWithFilterRequest) GetStoreIds() []int64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *PromotionDetailInfoWithFilterRequest) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterRequest) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterRequest) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterRequest) GetLastDate() string {
	if m != nil {
		return m.LastDate
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterRequest) GetSourceId() int64 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *PromotionDetailInfoWithFilterRequest) GetSize_() int64 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *PromotionDetailInfoWithFilterRequest) GetFrom() int64 {
	if m != nil {
		return m.From
	}
	return 0
}

type PromotionDetailInfoWithFilterResponse struct {
	PromotionCode             string            `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	ProductCode               string            `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	StoreId                   int64             `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Rules                     map[string]string `protobuf:"bytes,4,rep,name=rules,proto3" json:"rules,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Actions                   map[string]string `protobuf:"bytes,5,rep,name=actions,proto3" json:"actions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Channel                   string            `protobuf:"bytes,6,opt,name=channel,proto3" json:"channel,omitempty"`
	Source                    string            `protobuf:"bytes,7,opt,name=source,proto3" json:"source,omitempty"`
	StartDate                 string            `protobuf:"bytes,8,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	LastDate                  string            `protobuf:"bytes,9,opt,name=last_date,json=lastDate,proto3" json:"last_date,omitempty"`
	CreatedAt                 string            `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt                 string            `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Status                    string            `protobuf:"bytes,12,opt,name=status,proto3" json:"status,omitempty"`
	PromotionsDeletedAt       string            `protobuf:"bytes,13,opt,name=promotions_deleted_at,json=promotionsDeletedAt,proto3" json:"promotions_deleted_at,omitempty"`
	PromotionDetailsDeletedAt string            `protobuf:"bytes,14,opt,name=promotion_details_deleted_at,json=promotionDetailsDeletedAt,proto3" json:"promotion_details_deleted_at,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}          `json:"-"`
	XXX_unrecognized          []byte            `json:"-"`
	XXX_sizecache             int32             `json:"-"`
}

func (m *PromotionDetailInfoWithFilterResponse) Reset()         { *m = PromotionDetailInfoWithFilterResponse{} }
func (m *PromotionDetailInfoWithFilterResponse) String() string { return proto.CompactTextString(m) }
func (*PromotionDetailInfoWithFilterResponse) ProtoMessage()    {}
func (*PromotionDetailInfoWithFilterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{22}
}
func (m *PromotionDetailInfoWithFilterResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionDetailInfoWithFilterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionDetailInfoWithFilterResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionDetailInfoWithFilterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionDetailInfoWithFilterResponse.Merge(m, src)
}
func (m *PromotionDetailInfoWithFilterResponse) XXX_Size() int {
	return m.Size()
}
func (m *PromotionDetailInfoWithFilterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionDetailInfoWithFilterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionDetailInfoWithFilterResponse proto.InternalMessageInfo

func (m *PromotionDetailInfoWithFilterResponse) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *PromotionDetailInfoWithFilterResponse) GetRules() map[string]string {
	if m != nil {
		return m.Rules
	}
	return nil
}

func (m *PromotionDetailInfoWithFilterResponse) GetActions() map[string]string {
	if m != nil {
		return m.Actions
	}
	return nil
}

func (m *PromotionDetailInfoWithFilterResponse) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetStartDate() string {
	if m != nil {
		return m.StartDate
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetLastDate() string {
	if m != nil {
		return m.LastDate
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetPromotionsDeletedAt() string {
	if m != nil {
		return m.PromotionsDeletedAt
	}
	return ""
}

func (m *PromotionDetailInfoWithFilterResponse) GetPromotionDetailsDeletedAt() string {
	if m != nil {
		return m.PromotionDetailsDeletedAt
	}
	return ""
}

type PromotionDetailInfoWithFilterMultiResponse struct {
	PromotionDetailInfo  []*PromotionDetailInfoWithFilterResponse `protobuf:"bytes,1,rep,name=promotion_detail_info,json=promotionDetailInfo,proto3" json:"promotion_detail_info,omitempty"`
	Count                int64                                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *PromotionDetailInfoWithFilterMultiResponse) Reset() {
	*m = PromotionDetailInfoWithFilterMultiResponse{}
}
func (m *PromotionDetailInfoWithFilterMultiResponse) String() string {
	return proto.CompactTextString(m)
}
func (*PromotionDetailInfoWithFilterMultiResponse) ProtoMessage() {}
func (*PromotionDetailInfoWithFilterMultiResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{23}
}
func (m *PromotionDetailInfoWithFilterMultiResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionDetailInfoWithFilterMultiResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionDetailInfoWithFilterMultiResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionDetailInfoWithFilterMultiResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionDetailInfoWithFilterMultiResponse.Merge(m, src)
}
func (m *PromotionDetailInfoWithFilterMultiResponse) XXX_Size() int {
	return m.Size()
}
func (m *PromotionDetailInfoWithFilterMultiResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionDetailInfoWithFilterMultiResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionDetailInfoWithFilterMultiResponse proto.InternalMessageInfo

func (m *PromotionDetailInfoWithFilterMultiResponse) GetPromotionDetailInfo() []*PromotionDetailInfoWithFilterResponse {
	if m != nil {
		return m.PromotionDetailInfo
	}
	return nil
}

func (m *PromotionDetailInfoWithFilterMultiResponse) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type PromotionCodes struct {
	PromotionCodes       []string `protobuf:"bytes,1,rep,name=promotion_codes,json=promotionCodes,proto3" json:"promotion_codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionCodes) Reset()         { *m = PromotionCodes{} }
func (m *PromotionCodes) String() string { return proto.CompactTextString(m) }
func (*PromotionCodes) ProtoMessage()    {}
func (*PromotionCodes) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{24}
}
func (m *PromotionCodes) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionCodes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionCodes.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionCodes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionCodes.Merge(m, src)
}
func (m *PromotionCodes) XXX_Size() int {
	return m.Size()
}
func (m *PromotionCodes) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionCodes.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionCodes proto.InternalMessageInfo

func (m *PromotionCodes) GetPromotionCodes() []string {
	if m != nil {
		return m.PromotionCodes
	}
	return nil
}

type DatesAndStoreIDs struct {
	StoreIds             []int64  `protobuf:"varint,1,rep,packed,name=store_ids,json=storeIds,proto3" json:"store_ids,omitempty"`
	Dates                []string `protobuf:"bytes,2,rep,name=dates,proto3" json:"dates,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DatesAndStoreIDs) Reset()         { *m = DatesAndStoreIDs{} }
func (m *DatesAndStoreIDs) String() string { return proto.CompactTextString(m) }
func (*DatesAndStoreIDs) ProtoMessage()    {}
func (*DatesAndStoreIDs) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{25}
}
func (m *DatesAndStoreIDs) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DatesAndStoreIDs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DatesAndStoreIDs.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DatesAndStoreIDs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatesAndStoreIDs.Merge(m, src)
}
func (m *DatesAndStoreIDs) XXX_Size() int {
	return m.Size()
}
func (m *DatesAndStoreIDs) XXX_DiscardUnknown() {
	xxx_messageInfo_DatesAndStoreIDs.DiscardUnknown(m)
}

var xxx_messageInfo_DatesAndStoreIDs proto.InternalMessageInfo

func (m *DatesAndStoreIDs) GetStoreIds() []int64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *DatesAndStoreIDs) GetDates() []string {
	if m != nil {
		return m.Dates
	}
	return nil
}

type StoreIDDateChannel struct {
	StoreId              int64    `protobuf:"varint,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Date                 string   `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	Channel              string   `protobuf:"bytes,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreIDDateChannel) Reset()         { *m = StoreIDDateChannel{} }
func (m *StoreIDDateChannel) String() string { return proto.CompactTextString(m) }
func (*StoreIDDateChannel) ProtoMessage()    {}
func (*StoreIDDateChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{26}
}
func (m *StoreIDDateChannel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreIDDateChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreIDDateChannel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreIDDateChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreIDDateChannel.Merge(m, src)
}
func (m *StoreIDDateChannel) XXX_Size() int {
	return m.Size()
}
func (m *StoreIDDateChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreIDDateChannel.DiscardUnknown(m)
}

var xxx_messageInfo_StoreIDDateChannel proto.InternalMessageInfo

func (m *StoreIDDateChannel) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *StoreIDDateChannel) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *StoreIDDateChannel) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

type DeletePromotionDetailsRequest struct {
	ProductCode          string   `protobuf:"bytes,1,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	StoreIds             []int64  `protobuf:"varint,2,rep,packed,name=store_ids,json=storeIds,proto3" json:"store_ids,omitempty"`
	PromotionCodes       []string `protobuf:"bytes,3,rep,name=promotion_codes,json=promotionCodes,proto3" json:"promotion_codes,omitempty"`
	Channels             []string `protobuf:"bytes,4,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePromotionDetailsRequest) Reset()         { *m = DeletePromotionDetailsRequest{} }
func (m *DeletePromotionDetailsRequest) String() string { return proto.CompactTextString(m) }
func (*DeletePromotionDetailsRequest) ProtoMessage()    {}
func (*DeletePromotionDetailsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{27}
}
func (m *DeletePromotionDetailsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeletePromotionDetailsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeletePromotionDetailsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeletePromotionDetailsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePromotionDetailsRequest.Merge(m, src)
}
func (m *DeletePromotionDetailsRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeletePromotionDetailsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePromotionDetailsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePromotionDetailsRequest proto.InternalMessageInfo

func (m *DeletePromotionDetailsRequest) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *DeletePromotionDetailsRequest) GetStoreIds() []int64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *DeletePromotionDetailsRequest) GetPromotionCodes() []string {
	if m != nil {
		return m.PromotionCodes
	}
	return nil
}

func (m *DeletePromotionDetailsRequest) GetChannels() []string {
	if m != nil {
		return m.Channels
	}
	return nil
}

type DeletePromotionDetailsResponse struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	Errors               []string `protobuf:"bytes,2,rep,name=errors,proto3" json:"errors,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePromotionDetailsResponse) Reset()         { *m = DeletePromotionDetailsResponse{} }
func (m *DeletePromotionDetailsResponse) String() string { return proto.CompactTextString(m) }
func (*DeletePromotionDetailsResponse) ProtoMessage()    {}
func (*DeletePromotionDetailsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{28}
}
func (m *DeletePromotionDetailsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeletePromotionDetailsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeletePromotionDetailsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeletePromotionDetailsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePromotionDetailsResponse.Merge(m, src)
}
func (m *DeletePromotionDetailsResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeletePromotionDetailsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePromotionDetailsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePromotionDetailsResponse proto.InternalMessageInfo

func (m *DeletePromotionDetailsResponse) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

func (m *DeletePromotionDetailsResponse) GetErrors() []string {
	if m != nil {
		return m.Errors
	}
	return nil
}

type PromotionDetailByPromotionCodeRequest struct {
	PromotionCode        string   `protobuf:"bytes,1,opt,name=promotion_code,json=promotionCode,proto3" json:"promotion_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotionDetailByPromotionCodeRequest) Reset()         { *m = PromotionDetailByPromotionCodeRequest{} }
func (m *PromotionDetailByPromotionCodeRequest) String() string { return proto.CompactTextString(m) }
func (*PromotionDetailByPromotionCodeRequest) ProtoMessage()    {}
func (*PromotionDetailByPromotionCodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{29}
}
func (m *PromotionDetailByPromotionCodeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionDetailByPromotionCodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionDetailByPromotionCodeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionDetailByPromotionCodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionDetailByPromotionCodeRequest.Merge(m, src)
}
func (m *PromotionDetailByPromotionCodeRequest) XXX_Size() int {
	return m.Size()
}
func (m *PromotionDetailByPromotionCodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionDetailByPromotionCodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionDetailByPromotionCodeRequest proto.InternalMessageInfo

func (m *PromotionDetailByPromotionCodeRequest) GetPromotionCode() string {
	if m != nil {
		return m.PromotionCode
	}
	return ""
}

type StockItemPriceAllChannelAndPromotion struct {
	ProductCode          string                 `protobuf:"bytes,1,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	StoreId              int64                  `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Promotions           []*PromotionAllChannel `protobuf:"bytes,3,rep,name=promotions,proto3" json:"promotions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *StockItemPriceAllChannelAndPromotion) Reset()         { *m = StockItemPriceAllChannelAndPromotion{} }
func (m *StockItemPriceAllChannelAndPromotion) String() string { return proto.CompactTextString(m) }
func (*StockItemPriceAllChannelAndPromotion) ProtoMessage()    {}
func (*StockItemPriceAllChannelAndPromotion) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{30}
}
func (m *StockItemPriceAllChannelAndPromotion) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StockItemPriceAllChannelAndPromotion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StockItemPriceAllChannelAndPromotion.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StockItemPriceAllChannelAndPromotion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StockItemPriceAllChannelAndPromotion.Merge(m, src)
}
func (m *StockItemPriceAllChannelAndPromotion) XXX_Size() int {
	return m.Size()
}
func (m *StockItemPriceAllChannelAndPromotion) XXX_DiscardUnknown() {
	xxx_messageInfo_StockItemPriceAllChannelAndPromotion.DiscardUnknown(m)
}

var xxx_messageInfo_StockItemPriceAllChannelAndPromotion proto.InternalMessageInfo

func (m *StockItemPriceAllChannelAndPromotion) GetProductCode() string {
	if m != nil {
		return m.ProductCode
	}
	return ""
}

func (m *StockItemPriceAllChannelAndPromotion) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *StockItemPriceAllChannelAndPromotion) GetPromotions() []*PromotionAllChannel {
	if m != nil {
		return m.Promotions
	}
	return nil
}

type PromotionAllChannel struct {
	Code                 string            `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Channel              string            `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	Price                float64           `protobuf:"fixed64,3,opt,name=price,proto3" json:"price,omitempty"`
	Rules                map[string]string `protobuf:"bytes,4,rep,name=rules,proto3" json:"rules,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Actions              map[string]string `protobuf:"bytes,5,rep,name=actions,proto3" json:"actions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PromotionAllChannel) Reset()         { *m = PromotionAllChannel{} }
func (m *PromotionAllChannel) String() string { return proto.CompactTextString(m) }
func (*PromotionAllChannel) ProtoMessage()    {}
func (*PromotionAllChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{31}
}
func (m *PromotionAllChannel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotionAllChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotionAllChannel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotionAllChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotionAllChannel.Merge(m, src)
}
func (m *PromotionAllChannel) XXX_Size() int {
	return m.Size()
}
func (m *PromotionAllChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotionAllChannel.DiscardUnknown(m)
}

var xxx_messageInfo_PromotionAllChannel proto.InternalMessageInfo

func (m *PromotionAllChannel) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *PromotionAllChannel) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *PromotionAllChannel) GetPrice() float64 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PromotionAllChannel) GetRules() map[string]string {
	if m != nil {
		return m.Rules
	}
	return nil
}

func (m *PromotionAllChannel) GetActions() map[string]string {
	if m != nil {
		return m.Actions
	}
	return nil
}

type PromotedStockItemRequest struct {
	Dates                []string `protobuf:"bytes,1,rep,name=dates,proto3" json:"dates,omitempty"`
	StoreIds             []int64  `protobuf:"varint,2,rep,packed,name=store_ids,json=storeIds,proto3" json:"store_ids,omitempty"`
	CountryCode          string   `protobuf:"bytes,3,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PromotedStockItemRequest) Reset()         { *m = PromotedStockItemRequest{} }
func (m *PromotedStockItemRequest) String() string { return proto.CompactTextString(m) }
func (*PromotedStockItemRequest) ProtoMessage()    {}
func (*PromotedStockItemRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{32}
}
func (m *PromotedStockItemRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PromotedStockItemRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PromotedStockItemRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PromotedStockItemRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PromotedStockItemRequest.Merge(m, src)
}
func (m *PromotedStockItemRequest) XXX_Size() int {
	return m.Size()
}
func (m *PromotedStockItemRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PromotedStockItemRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PromotedStockItemRequest proto.InternalMessageInfo

func (m *PromotedStockItemRequest) GetDates() []string {
	if m != nil {
		return m.Dates
	}
	return nil
}

func (m *PromotedStockItemRequest) GetStoreIds() []int64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *PromotedStockItemRequest) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

type OK struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OK) Reset()         { *m = OK{} }
func (m *OK) String() string { return proto.CompactTextString(m) }
func (*OK) ProtoMessage()    {}
func (*OK) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{33}
}
func (m *OK) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OK) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OK.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OK) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OK.Merge(m, src)
}
func (m *OK) XXX_Size() int {
	return m.Size()
}
func (m *OK) XXX_DiscardUnknown() {
	xxx_messageInfo_OK.DiscardUnknown(m)
}

var xxx_messageInfo_OK proto.InternalMessageInfo

func (m *OK) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

type ExclusivePromoRequest struct {
	StoreId              int64    `protobuf:"varint,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	CountryCode          string   `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Channel              string   `protobuf:"bytes,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExclusivePromoRequest) Reset()         { *m = ExclusivePromoRequest{} }
func (m *ExclusivePromoRequest) String() string { return proto.CompactTextString(m) }
func (*ExclusivePromoRequest) ProtoMessage()    {}
func (*ExclusivePromoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{34}
}
func (m *ExclusivePromoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExclusivePromoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExclusivePromoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExclusivePromoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExclusivePromoRequest.Merge(m, src)
}
func (m *ExclusivePromoRequest) XXX_Size() int {
	return m.Size()
}
func (m *ExclusivePromoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExclusivePromoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExclusivePromoRequest proto.InternalMessageInfo

func (m *ExclusivePromoRequest) GetStoreId() int64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *ExclusivePromoRequest) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *ExclusivePromoRequest) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

type Null struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Null) Reset()         { *m = Null{} }
func (m *Null) String() string { return proto.CompactTextString(m) }
func (*Null) ProtoMessage()    {}
func (*Null) Descriptor() ([]byte, []int) {
	return fileDescriptor_93258b77978a8f53, []int{35}
}
func (m *Null) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Null) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Null.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Null) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Null.Merge(m, src)
}
func (m *Null) XXX_Size() int {
	return m.Size()
}
func (m *Null) XXX_DiscardUnknown() {
	xxx_messageInfo_Null.DiscardUnknown(m)
}

var xxx_messageInfo_Null proto.InternalMessageInfo

func init() {
	proto.RegisterType((*PromotionQuotaDetailInfoWithFilterMultiResponse)(nil), "aloha.promo.PromotionQuotaDetailInfoWithFilterMultiResponse")
	proto.RegisterType((*PromotionQuotaDetailInfoWithFilterResponse)(nil), "aloha.promo.PromotionQuotaDetailInfoWithFilterResponse")
	proto.RegisterType((*ImportPromotionQuotaRequest)(nil), "aloha.promo.ImportPromotionQuotaRequest")
	proto.RegisterType((*ImportPromotionQuotaRequestMultiple)(nil), "aloha.promo.ImportPromotionQuotaRequestMultiple")
	proto.RegisterType((*ImportPromotionQuotaResponse)(nil), "aloha.promo.ImportPromotionQuotaResponse")
	proto.RegisterType((*ImportPromotionQuotaError)(nil), "aloha.promo.ImportPromotionQuotaError")
	proto.RegisterType((*ImportPromotionDetail)(nil), "aloha.promo.ImportPromotionDetail")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.ImportPromotionDetail.ActionsEntry")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.ImportPromotionDetail.RulesEntry")
	proto.RegisterType((*ImportPromotionDetailRequest)(nil), "aloha.promo.ImportPromotionDetailRequest")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.ImportPromotionDetailRequest.ActionsEntry")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.ImportPromotionDetailRequest.RulesEntry")
	proto.RegisterType((*PromotionChannelRequest)(nil), "aloha.promo.PromotionChannelRequest")
	proto.RegisterType((*PromotionChannelResponse)(nil), "aloha.promo.PromotionChannelResponse")
	proto.RegisterMapType((map[string]int64)(nil), "aloha.promo.PromotionChannelResponse.PromotionChannelInfoEntry")
	proto.RegisterType((*StockItemPromotionMultiRequest)(nil), "aloha.promo.StockItemPromotionMultiRequest")
	proto.RegisterType((*StockItemPromotionResponse)(nil), "aloha.promo.StockItemPromotionResponse")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.StockItemPromotionResponse.ActionsEntry")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.StockItemPromotionResponse.RulesEntry")
	proto.RegisterType((*StockItemPromotionMultiResponse)(nil), "aloha.promo.StockItemPromotionMultiResponse")
	proto.RegisterType((*ProductCodes)(nil), "aloha.promo.ProductCodes")
	proto.RegisterType((*ImportPromotionRequest)(nil), "aloha.promo.ImportPromotionRequest")
	proto.RegisterType((*ImportPromotionMultiRequest)(nil), "aloha.promo.ImportPromotionMultiRequest")
	proto.RegisterType((*ImportPromotionResponse)(nil), "aloha.promo.ImportPromotionResponse")
	proto.RegisterType((*ImportPromotionError)(nil), "aloha.promo.ImportPromotionError")
	proto.RegisterType((*PromotionSource)(nil), "aloha.promo.PromotionSource")
	proto.RegisterType((*PromotionSourceRequest)(nil), "aloha.promo.PromotionSourceRequest")
	proto.RegisterType((*PromotionSourceResponse)(nil), "aloha.promo.PromotionSourceResponse")
	proto.RegisterType((*PromotionDetailInfoWithFilterRequest)(nil), "aloha.promo.PromotionDetailInfoWithFilterRequest")
	proto.RegisterType((*PromotionDetailInfoWithFilterResponse)(nil), "aloha.promo.PromotionDetailInfoWithFilterResponse")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.PromotionDetailInfoWithFilterResponse.ActionsEntry")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.PromotionDetailInfoWithFilterResponse.RulesEntry")
	proto.RegisterType((*PromotionDetailInfoWithFilterMultiResponse)(nil), "aloha.promo.PromotionDetailInfoWithFilterMultiResponse")
	proto.RegisterType((*PromotionCodes)(nil), "aloha.promo.PromotionCodes")
	proto.RegisterType((*DatesAndStoreIDs)(nil), "aloha.promo.DatesAndStoreIDs")
	proto.RegisterType((*StoreIDDateChannel)(nil), "aloha.promo.StoreIDDateChannel")
	proto.RegisterType((*DeletePromotionDetailsRequest)(nil), "aloha.promo.DeletePromotionDetailsRequest")
	proto.RegisterType((*DeletePromotionDetailsResponse)(nil), "aloha.promo.DeletePromotionDetailsResponse")
	proto.RegisterType((*PromotionDetailByPromotionCodeRequest)(nil), "aloha.promo.PromotionDetailByPromotionCodeRequest")
	proto.RegisterType((*StockItemPriceAllChannelAndPromotion)(nil), "aloha.promo.StockItemPriceAllChannelAndPromotion")
	proto.RegisterType((*PromotionAllChannel)(nil), "aloha.promo.PromotionAllChannel")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.PromotionAllChannel.ActionsEntry")
	proto.RegisterMapType((map[string]string)(nil), "aloha.promo.PromotionAllChannel.RulesEntry")
	proto.RegisterType((*PromotedStockItemRequest)(nil), "aloha.promo.PromotedStockItemRequest")
	proto.RegisterType((*OK)(nil), "aloha.promo.OK")
	proto.RegisterType((*ExclusivePromoRequest)(nil), "aloha.promo.ExclusivePromoRequest")
	proto.RegisterType((*Null)(nil), "aloha.promo.Null")
}

func init() { proto.RegisterFile("promotion.proto", fileDescriptor_93258b77978a8f53) }

var fileDescriptor_93258b77978a8f53 = []byte{
	// 1990 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcd, 0x6f, 0xdc, 0xc6,
	0x15, 0x17, 0xc9, 0xdd, 0xd5, 0xee, 0x93, 0x2c, 0x3b, 0xa3, 0x95, 0xcc, 0x5d, 0xd9, 0xf2, 0x9a,
	0xb6, 0x9b, 0x4d, 0x9c, 0x28, 0x89, 0xd2, 0x36, 0x69, 0xd0, 0x22, 0x5d, 0xdb, 0x8a, 0xa2, 0x1a,
	0xb5, 0x55, 0x0a, 0x41, 0xfa, 0x81, 0x60, 0xc1, 0x2c, 0x69, 0x8b, 0x11, 0x45, 0x6e, 0xc8, 0x59,
	0x35, 0x2a, 0x5a, 0xa3, 0x28, 0x72, 0x0e, 0x9a, 0x43, 0x81, 0xde, 0x7a, 0x68, 0xd1, 0x02, 0x3d,
	0xf6, 0xd2, 0x63, 0xaf, 0x3d, 0xf6, 0x58, 0xa0, 0x97, 0x42, 0x3d, 0xf6, 0x9f, 0x28, 0x38, 0x33,
	0x24, 0x87, 0x43, 0xee, 0x2c, 0xb7, 0x51, 0xa0, 0xdc, 0x38, 0x1f, 0xef, 0xcd, 0x9b, 0x37, 0xbf,
	0xf7, 0xde, 0x6f, 0x66, 0x17, 0x2e, 0x8f, 0xc3, 0xe0, 0x38, 0xc0, 0x6e, 0xe0, 0x6f, 0x8d, 0xc3,
	0x00, 0x07, 0x68, 0xc9, 0xf2, 0x82, 0x43, 0x6b, 0x8b, 0x74, 0x1b, 0x7f, 0x53, 0xe0, 0x95, 0xfd,
	0x64, 0xc2, 0x0f, 0x26, 0x01, 0xb6, 0x1e, 0x38, 0xd8, 0x72, 0xbd, 0x3d, 0xff, 0x49, 0xf0, 0xbe,
	0x8b, 0x0f, 0xdf, 0x71, 0x3d, 0xec, 0x84, 0xdf, 0x9f, 0x78, 0xd8, 0x35, 0x9d, 0x68, 0x1c, 0xf8,
	0x91, 0x83, 0x4e, 0x60, 0x23, 0xd5, 0x39, 0xfc, 0x38, 0x96, 0x19, 0xda, 0x44, 0x68, 0xe8, 0xfa,
	0x4f, 0x02, 0x5d, 0xe9, 0x69, 0xfd, 0xa5, 0xed, 0x37, 0xb6, 0xb8, 0x65, 0xb6, 0x66, 0x2f, 0x91,
	0x68, 0x37, 0xf5, 0xf1, 0x94, 0xb9, 0xa8, 0x0d, 0xf5, 0x51, 0x30, 0xf1, 0xb1, 0xae, 0xf6, 0x94,
	0xbe, 0x66, 0xd2, 0x86, 0x71, 0xa6, 0xc0, 0x8b, 0xd5, 0xd5, 0xa3, 0x3b, 0xb0, 0x92, 0x19, 0x3f,
	0x0a, 0x6c, 0x47, 0x57, 0x7a, 0x4a, 0xbf, 0x65, 0x5e, 0x4a, 0x7b, 0xef, 0x07, 0xb6, 0x83, 0x6e,
	0xc2, 0xf2, 0x38, 0x0c, 0xec, 0xc9, 0x08, 0xd3, 0x49, 0x2a, 0x99, 0xb4, 0xc4, 0xfa, 0xc8, 0x94,
	0x36, 0xd4, 0xc9, 0xe6, 0x75, 0xad, 0xa7, 0xf4, 0xeb, 0x26, 0x6d, 0xa0, 0x75, 0x68, 0x58, 0x23,
	0xec, 0x9e, 0x38, 0x7a, 0xad, 0xa7, 0xf4, 0x9b, 0x26, 0x6b, 0xa1, 0xeb, 0x00, 0xa3, 0xd0, 0xb1,
	0xb0, 0x63, 0x0f, 0x2d, 0xac, 0xd7, 0x89, 0xba, 0x16, 0xeb, 0x19, 0xe0, 0x78, 0x78, 0x32, 0xb6,
	0x93, 0xe1, 0x06, 0x1d, 0x66, 0x3d, 0x03, 0x6c, 0xfc, 0x46, 0x81, 0x8d, 0xbd, 0xe3, 0x71, 0x10,
	0xe2, 0xfc, 0x56, 0x4d, 0xe7, 0xe3, 0x89, 0x13, 0xe1, 0x8b, 0xda, 0x95, 0xf1, 0xb9, 0x02, 0xb7,
	0x24, 0x76, 0x11, 0xdc, 0x8c, 0x3d, 0x07, 0x7d, 0x24, 0x35, 0x9f, 0x41, 0xa6, 0x9f, 0x83, 0x8c,
	0x64, 0xbe, 0x29, 0x53, 0x66, 0x7c, 0xaa, 0xc0, 0xb5, 0xf2, 0x71, 0x06, 0x01, 0x1b, 0x3a, 0x6e,
	0xc9, 0xf8, 0x4e, 0x18, 0x06, 0x21, 0x33, 0xe5, 0x6b, 0x33, 0x4d, 0x21, 0xb3, 0xcd, 0xe9, 0x8a,
	0x8c, 0x5f, 0x40, 0x67, 0xaa, 0xdc, 0x39, 0x9e, 0xd7, 0x3a, 0x34, 0x9c, 0x58, 0x65, 0xa4, 0x6b,
	0x3d, 0xad, 0xdf, 0x32, 0x59, 0xcb, 0xf8, 0x4c, 0x83, 0x35, 0x61, 0x7d, 0x1a, 0x17, 0x05, 0xa5,
	0x4a, 0x51, 0x69, 0x07, 0x9a, 0x11, 0x0e, 0x42, 0x67, 0xe8, 0xda, 0x2c, 0xd8, 0x16, 0x49, 0x7b,
	0xcf, 0x26, 0x38, 0x3e, 0xb4, 0x7c, 0xdf, 0xf1, 0xe2, 0x41, 0x8d, 0x0c, 0xb6, 0x58, 0xcf, 0x9e,
	0x8d, 0x10, 0xd4, 0xf0, 0xe9, 0x98, 0xc2, 0xa4, 0x65, 0x92, 0x6f, 0x74, 0x1f, 0xea, 0xe1, 0xc4,
	0x73, 0x22, 0xbd, 0x4e, 0x7c, 0xfb, 0xb2, 0xcc, 0xb7, 0xd4, 0xc6, 0x2d, 0x33, 0x9e, 0xbf, 0xe3,
	0xe3, 0xf0, 0xd4, 0xa4, 0xb2, 0x68, 0x0f, 0x16, 0x63, 0xcc, 0x05, 0x7e, 0xa4, 0x37, 0x88, 0x9a,
	0x57, 0x2a, 0xa8, 0x19, 0x50, 0x09, 0xaa, 0x28, 0x91, 0xef, 0xbe, 0x09, 0x90, 0xe9, 0x47, 0x57,
	0x40, 0x3b, 0x72, 0x4e, 0x99, 0x17, 0xe2, 0xcf, 0x38, 0x04, 0x4e, 0x2c, 0x6f, 0x92, 0xb8, 0x9b,
	0x36, 0xde, 0x52, 0xdf, 0x54, 0xba, 0x6f, 0xc1, 0x32, 0xaf, 0x72, 0x1e, 0x59, 0xe3, 0xf7, 0xb5,
	0x02, 0x2c, 0xa9, 0x95, 0x73, 0xc6, 0xf0, 0x06, 0xb4, 0xa2, 0x60, 0x12, 0x8e, 0xb8, 0xc3, 0x69,
	0xd2, 0x0e, 0x7a, 0x3a, 0x11, 0xb6, 0x42, 0x3c, 0x8c, 0x13, 0x07, 0x39, 0x9d, 0x96, 0xd9, 0x22,
	0x3d, 0x0f, 0x2c, 0x4c, 0x64, 0x3d, 0x2b, 0x62, 0xa3, 0xf4, 0x88, 0x9a, 0x71, 0x07, 0x19, 0x14,
	0x71, 0x51, 0x97, 0xe3, 0xa2, 0x21, 0xc3, 0xc5, 0xe2, 0x34, 0x5c, 0x34, 0x39, 0x5c, 0x7c, 0x2f,
	0xc1, 0x45, 0x8b, 0x1c, 0xe8, 0xd7, 0x67, 0x1f, 0x28, 0x73, 0x55, 0x09, 0x3c, 0xf6, 0x33, 0x78,
	0x00, 0xd1, 0xf6, 0xcd, 0xea, 0xda, 0xbe, 0x4a, 0x28, 0xe9, 0xc0, 0xd5, 0xd4, 0xca, 0xfb, 0xd4,
	0x7b, 0x49, 0x5e, 0xfb, 0xa7, 0x02, 0x7a, 0x71, 0x8c, 0xe5, 0xb4, 0x08, 0xda, 0xe2, 0xd8, 0x5e,
	0x56, 0x8c, 0xdf, 0x2e, 0x2f, 0xc6, 0x82, 0x92, 0xad, 0x32, 0x0d, 0xd4, 0x2b, 0xa5, 0xca, 0xbb,
	0xbb, 0xd0, 0x99, 0x2a, 0x32, 0x6b, 0xd7, 0x1a, 0xbf, 0xeb, 0xdf, 0x29, 0xb0, 0x79, 0x80, 0x83,
	0xd1, 0xd1, 0x1e, 0x76, 0x8e, 0x53, 0x95, 0x8c, 0x75, 0xd0, 0xe8, 0xb8, 0x05, 0x97, 0x78, 0x74,
	0x46, 0x64, 0x67, 0x2d, 0x73, 0x99, 0x83, 0x67, 0x44, 0x62, 0x83, 0xe1, 0x33, 0xd2, 0x55, 0x32,
	0xa1, 0xc9, 0x00, 0x1a, 0x21, 0x1d, 0x16, 0x19, 0x1e, 0x59, 0x60, 0x24, 0xcd, 0x18, 0xf9, 0x84,
	0x4b, 0x84, 0xa7, 0x14, 0xf9, 0x34, 0x32, 0x96, 0x58, 0x5f, 0xac, 0xda, 0xf8, 0x6b, 0x0d, 0xba,
	0x45, 0x0b, 0xe7, 0x65, 0x15, 0xf9, 0xf0, 0x54, 0xa5, 0xe1, 0xa9, 0xcd, 0x08, 0xcf, 0x9a, 0x3c,
	0x3c, 0xeb, 0xf9, 0xf0, 0x4c, 0xe2, 0xaf, 0xc1, 0xc5, 0x1f, 0xe7, 0x90, 0xc5, 0xbc, 0x43, 0xde,
	0x4d, 0x22, 0xb3, 0x49, 0xe0, 0xb3, 0x9d, 0x83, 0xcf, 0x74, 0x37, 0x94, 0xc4, 0xe5, 0xa3, 0x2c,
	0x2e, 0xcb, 0xa2, 0x5c, 0xa2, 0xab, 0x34, 0x2a, 0x63, 0x0f, 0xda, 0x8e, 0xe7, 0x30, 0x9e, 0x04,
	0xd4, 0x83, 0xac, 0x67, 0x80, 0xf3, 0xc9, 0x71, 0x29, 0x9f, 0x1c, 0x2f, 0x28, 0xa2, 0x9f, 0xc1,
	0x8d, 0xa9, 0xd0, 0x66, 0xe8, 0xf9, 0x09, 0xac, 0x45, 0xf1, 0x94, 0xa1, 0x8b, 0x9d, 0xe3, 0x61,
	0x0a, 0x99, 0x88, 0x45, 0xef, 0xf3, 0x15, 0x5d, 0x66, 0xae, 0x46, 0x85, 0xb1, 0xc8, 0x78, 0x1d,
	0x96, 0xf7, 0xf9, 0x18, 0xa9, 0x12, 0x48, 0xc6, 0x9f, 0x54, 0x58, 0x17, 0x72, 0xe6, 0x79, 0x96,
	0xa9, 0xc7, 0xf0, 0x5c, 0xa6, 0x83, 0xde, 0x1d, 0x28, 0x7f, 0x59, 0xda, 0x36, 0x2a, 0xe4, 0xed,
	0x2b, 0xe3, 0x7c, 0x47, 0x24, 0x04, 0x56, 0x4d, 0x1a, 0x58, 0x75, 0x21, 0xb0, 0xf2, 0xcc, 0xbc,
	0x21, 0x67, 0xe6, 0x8b, 0x22, 0x33, 0xff, 0x79, 0x81, 0xd9, 0xe6, 0xd2, 0xd6, 0x07, 0xa0, 0x53,
	0x8a, 0x98, 0x1d, 0xeb, 0x30, 0xcc, 0xb1, 0xde, 0x5b, 0xb2, 0x0d, 0x27, 0x84, 0x77, 0xdd, 0x2d,
	0xed, 0x37, 0xc6, 0x70, 0xb5, 0x20, 0xc1, 0x40, 0xf5, 0x1e, 0xb4, 0x05, 0x21, 0x9e, 0xe0, 0xde,
	0x94, 0xad, 0x4a, 0xb9, 0x6d, 0xa9, 0xb8, 0xf1, 0x17, 0x05, 0xda, 0x65, 0xd3, 0xcf, 0x91, 0xd2,
	0xf2, 0x69, 0x4c, 0x93, 0xb1, 0x8c, 0x9a, 0xc8, 0x32, 0x32, 0x32, 0x5c, 0xcf, 0x91, 0xe1, 0x6f,
	0xc0, 0xe5, 0xd4, 0xda, 0x03, 0x02, 0x42, 0xb4, 0x02, 0xaa, 0x6b, 0x13, 0x13, 0x35, 0x53, 0x75,
	0x49, 0x82, 0xf4, 0xad, 0xe3, 0xc4, 0x1e, 0xf2, 0x6d, 0xe8, 0xb0, 0x2e, 0x88, 0x25, 0x7e, 0xff,
	0x90, 0x2b, 0xd3, 0xc9, 0x08, 0xf3, 0xfb, 0x2e, 0x64, 0xf0, 0x1c, 0x52, 0xc4, 0x33, 0x9f, 0x5f,
	0x2b, 0xaf, 0xc2, 0x4c, 0x3e, 0xbb, 0xa7, 0xd3, 0x0e, 0xe3, 0x33, 0x15, 0x6e, 0x0b, 0xc8, 0x17,
	0xef, 0xb4, 0x14, 0x63, 0xb9, 0xaa, 0x17, 0x2f, 0xa5, 0x71, 0x55, 0xaf, 0x82, 0xbf, 0x8b, 0x27,
	0xa7, 0xcd, 0x2e, 0x5e, 0xf3, 0xc5, 0x58, 0x2e, 0x1b, 0x34, 0x84, 0x6c, 0x80, 0xa0, 0x16, 0xb9,
	0x3f, 0x73, 0x18, 0x69, 0x24, 0xdf, 0x71, 0xdf, 0x93, 0x30, 0x38, 0x26, 0x7c, 0x51, 0x33, 0xc9,
	0xb7, 0xf1, 0xdf, 0x3a, 0xdc, 0x99, 0xe1, 0x90, 0x73, 0xbf, 0xe4, 0x4b, 0xb0, 0x78, 0x90, 0x14,
	0xc9, 0x1a, 0x39, 0xdd, 0xef, 0x94, 0x9f, 0xae, 0xcc, 0xce, 0x92, 0x7a, 0xf9, 0xa3, 0xac, 0x5e,
	0xd6, 0x65, 0xd4, 0x4d, 0xaa, 0xb6, 0xbc, 0x74, 0x72, 0xe5, 0xbe, 0x91, 0x2f, 0xf7, 0xeb, 0xd0,
	0x60, 0x40, 0xa5, 0xe9, 0x8d, 0xb5, 0x84, 0x13, 0x6f, 0x4a, 0x4f, 0xbc, 0x25, 0xcd, 0xaa, 0x20,
	0xcf, 0xaa, 0x4b, 0x42, 0x56, 0x25, 0x16, 0x61, 0x0b, 0x4f, 0x22, 0x7d, 0x99, 0x59, 0x44, 0x5a,
	0x68, 0x1b, 0xd6, 0xb2, 0xf2, 0x38, 0xe4, 0x98, 0xc0, 0x25, 0x32, 0x6d, 0x35, 0x1b, 0x7c, 0x90,
	0x72, 0x82, 0xb7, 0xe1, 0x5a, 0xa1, 0xd8, 0xf0, 0xa2, 0x2b, 0x44, 0xb4, 0x23, 0xd6, 0x94, 0x54,
	0xc1, 0x05, 0xf1, 0x86, 0x3f, 0xf3, 0xef, 0x5a, 0xb3, 0x1f, 0xe5, 0x9e, 0x70, 0x9e, 0x29, 0x79,
	0x8e, 0xdb, 0x9e, 0x1f, 0x46, 0x9c, 0x37, 0x67, 0x3e, 0xc2, 0x7d, 0x0b, 0x56, 0xf6, 0xf9, 0xd0,
	0x8a, 0xd0, 0xf3, 0xdc, 0xc3, 0x63, 0x8e, 0x68, 0xac, 0xe4, 0x62, 0x30, 0x32, 0x76, 0xe0, 0x4a,
	0x0c, 0x98, 0x68, 0xe0, 0xdb, 0x07, 0x24, 0xb2, 0x1e, 0x44, 0xf2, 0x8c, 0xd6, 0x86, 0x7a, 0x0c,
	0x93, 0x84, 0xe0, 0xd3, 0x86, 0xf1, 0x01, 0x20, 0x26, 0x1e, 0x6b, 0x63, 0xb7, 0x91, 0x5c, 0xf8,
	0x2a, 0x05, 0x46, 0xcc, 0xb1, 0x70, 0xf2, 0x3d, 0xfd, 0x8a, 0x60, 0xfc, 0x41, 0x81, 0xeb, 0x14,
	0x11, 0x82, 0xef, 0xa2, 0x24, 0x0b, 0x57, 0x78, 0x56, 0x29, 0x5c, 0x4f, 0xf8, 0x6d, 0x95, 0x38,
	0x4c, 0x2b, 0x73, 0x18, 0xea, 0x42, 0x93, 0x59, 0x45, 0x53, 0x4f, 0xcb, 0x4c, 0xdb, 0xc6, 0xbb,
	0xb0, 0x39, 0xcd, 0x4a, 0x86, 0x93, 0x15, 0x50, 0x83, 0x23, 0x62, 0x5c, 0xd3, 0x54, 0x83, 0x23,
	0xae, 0x64, 0xaa, 0xb9, 0x92, 0xf9, 0xa8, 0x90, 0x6b, 0xef, 0x9d, 0xe6, 0x8e, 0x78, 0x3e, 0x3e,
	0x68, 0xfc, 0x51, 0x81, 0xdb, 0x1c, 0x75, 0x75, 0x47, 0xce, 0xc0, 0xf3, 0xd8, 0x31, 0x0d, 0x7c,
	0x3b, 0x55, 0xfd, 0x05, 0x9f, 0xa7, 0xbe, 0x0b, 0xc0, 0xf1, 0x67, 0x4a, 0x29, 0x7b, 0xe5, 0xd8,
	0xcf, 0xd6, 0x37, 0x39, 0x19, 0xe3, 0x5f, 0x2a, 0xac, 0x96, 0xcc, 0x89, 0xf1, 0xc2, 0xd9, 0x43,
	0xbe, 0x79, 0xbc, 0xa8, 0xf9, 0x94, 0xda, 0x86, 0xfa, 0x38, 0xde, 0x24, 0xc1, 0x91, 0x62, 0xd2,
	0x06, 0x1a, 0xe4, 0x4b, 0xc6, 0xdd, 0x59, 0x86, 0x95, 0x14, 0x88, 0x5d, 0xb1, 0x40, 0xbc, 0x3c,
	0x53, 0xc9, 0x57, 0xe9, 0x7d, 0x63, 0x9c, 0xbc, 0x61, 0x38, 0x76, 0x8a, 0x86, 0x04, 0x49, 0x69,
	0x60, 0x2b, 0x5c, 0x60, 0xcb, 0x83, 0x46, 0xbc, 0xb9, 0x6b, 0xc5, 0x9b, 0x7b, 0x1b, 0xd4, 0xc7,
	0x0f, 0x45, 0xd8, 0x1b, 0x01, 0xac, 0xed, 0x7c, 0x32, 0xf2, 0x26, 0x91, 0x7b, 0x42, 0x63, 0x25,
	0x31, 0x42, 0x92, 0x31, 0xc4, 0xc5, 0xd4, 0xc2, 0x62, 0x92, 0x04, 0xd2, 0x80, 0xda, 0xa3, 0x89,
	0xe7, 0x6d, 0xff, 0x6a, 0x15, 0xb4, 0xc1, 0xd8, 0x45, 0x3f, 0x05, 0x7d, 0xd7, 0xc1, 0x03, 0xcf,
	0x2b, 0xde, 0xe7, 0xd0, 0xdd, 0x19, 0x17, 0x3e, 0xfe, 0x86, 0xd1, 0x7d, 0xa9, 0xda, 0x64, 0x1a,
	0xfe, 0xc6, 0x02, 0x7a, 0x06, 0x37, 0xa6, 0x2d, 0x9c, 0x40, 0xfd, 0x4b, 0x5d, 0xff, 0xd7, 0x0a,
	0xf4, 0x76, 0x1d, 0x2c, 0x2d, 0x41, 0xe8, 0xb5, 0x79, 0xca, 0x15, 0xb5, 0xe3, 0x8d, 0xea, 0x22,
	0xa2, 0x49, 0xef, 0xc3, 0xd5, 0xd4, 0x22, 0xc7, 0xce, 0xdd, 0x96, 0x6f, 0x88, 0xbb, 0x13, 0x2a,
	0x4c, 0xb7, 0x23, 0x2e, 0x9b, 0x5d, 0xa2, 0x17, 0xd0, 0x47, 0xd0, 0x79, 0xc7, 0xf5, 0x6d, 0xe2,
	0x6c, 0x2b, 0xc4, 0xae, 0xff, 0x34, 0xbb, 0x98, 0xa3, 0xeb, 0x39, 0x49, 0xb1, 0x06, 0xce, 0xed,
	0x57, 0x17, 0x36, 0x0e, 0x70, 0xe8, 0x58, 0xc7, 0xff, 0xd7, 0x6a, 0x55, 0xdf, 0x18, 0x8c, 0x85,
	0x57, 0x15, 0x74, 0x08, 0x57, 0xd9, 0xb6, 0x76, 0x7c, 0xfb, 0x4b, 0xdd, 0xd4, 0x53, 0xe8, 0xa4,
	0x9b, 0x9a, 0x77, 0xad, 0xb9, 0xb6, 0x34, 0x82, 0xcb, 0xc2, 0xad, 0x16, 0x49, 0x7f, 0x8e, 0xca,
	0x85, 0xc0, 0x6d, 0xf9, 0x15, 0x3e, 0xdd, 0x8d, 0x07, 0x6b, 0x74, 0x37, 0xe2, 0x52, 0x2f, 0x54,
	0x7e, 0xac, 0xae, 0xba, 0x56, 0x5f, 0x41, 0xf7, 0xe0, 0x8a, 0xc0, 0x05, 0x22, 0xb4, 0x31, 0xe5,
	0x21, 0x38, 0xc6, 0x6b, 0xf7, 0x72, 0x6e, 0xf0, 0xf1, 0x43, 0x63, 0x01, 0x7d, 0xaa, 0xc0, 0xcd,
	0x62, 0xb0, 0x0a, 0x4c, 0x00, 0x49, 0xc9, 0x65, 0x39, 0x6d, 0x98, 0xef, 0x74, 0x6c, 0x58, 0xe5,
	0xad, 0x48, 0xf2, 0xd4, 0xed, 0x19, 0xcf, 0xda, 0x74, 0xa5, 0x3b, 0x95, 0x1e, 0xbf, 0x8d, 0x05,
	0x64, 0x01, 0xe2, 0x57, 0x61, 0x0f, 0x05, 0xb7, 0xa4, 0xb7, 0xf6, 0xd2, 0x53, 0x99, 0xf2, 0x34,
	0x60, 0x2c, 0xa0, 0x4f, 0xa0, 0x23, 0x9c, 0xc9, 0x3d, 0x2b, 0x72, 0xec, 0xc7, 0xfe, 0xc1, 0xc3,
	0xf7, 0xd0, 0x8b, 0x79, 0x3c, 0xcb, 0xd8, 0x66, 0xf7, 0x6e, 0xa5, 0xb9, 0x1c, 0x1a, 0x9e, 0xc1,
	0x26, 0x8b, 0x24, 0xf2, 0xcb, 0x6d, 0xa1, 0x08, 0x47, 0xa8, 0xcc, 0x4f, 0xc5, 0x2a, 0xdd, 0x7d,
	0x6d, 0xda, 0xc1, 0x4d, 0xa5, 0x74, 0xe4, 0x08, 0x7f, 0xa9, 0x40, 0x8f, 0x81, 0xdf, 0xb7, 0x2e,
	0xc6, 0x84, 0x97, 0xa0, 0xb6, 0xef, 0xfa, 0x4f, 0xd1, 0x73, 0x39, 0xf1, 0xb8, 0x2a, 0x97, 0x41,
	0x7f, 0x52, 0x78, 0xe7, 0x22, 0xbf, 0xdf, 0xa2, 0x57, 0xab, 0xfe, 0x4a, 0x9d, 0xfc, 0xf8, 0xdd,
	0x7d, 0xa1, 0x82, 0x44, 0x8a, 0x90, 0x09, 0x74, 0x4b, 0x73, 0x04, 0x5d, 0xbc, 0xf2, 0x4f, 0xe4,
	0x73, 0x2d, 0xda, 0x57, 0xd0, 0xe7, 0x0a, 0xdc, 0xe1, 0xc1, 0x3f, 0xf5, 0x8f, 0x14, 0x73, 0x98,
	0xf0, 0xed, 0x39, 0xff, 0x02, 0x22, 0x26, 0xff, 0x1f, 0xc2, 0xda, 0xae, 0x83, 0xf3, 0x34, 0x8d,
	0xa4, 0xcb, 0xfc, 0x1b, 0x71, 0x29, 0x8f, 0x93, 0xd6, 0xe5, 0x7b, 0x6b, 0x7f, 0x3f, 0xdb, 0x54,
	0xfe, 0x71, 0xb6, 0xa9, 0xfc, 0xfb, 0x6c, 0x53, 0xf9, 0xed, 0x7f, 0x36, 0x17, 0x7e, 0xac, 0x85,
	0xe3, 0xd1, 0x87, 0x0d, 0xf2, 0x07, 0x99, 0xd7, 0xff, 0x17, 0x00, 0x00, 0xff, 0xff, 0x4f, 0x49,
	0x02, 0x48, 0x33, 0x23, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ApiClient is the client API for Api service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ApiClient interface {
	// Promotion API
	GetAllStockItemPromotion(ctx context.Context, in *StockItemPromotionMultiRequest, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error)
	GetAllStockItemPromotionChannel(ctx context.Context, in *StockItemPromotionMultiRequest, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error)
	GetPromotionDetailInfoWithFilter(ctx context.Context, in *PromotionDetailInfoWithFilterRequest, opts ...grpc.CallOption) (*PromotionDetailInfoWithFilterMultiResponse, error)
	GetPromotedProductCodes(ctx context.Context, in *StoreIDDateChannel, opts ...grpc.CallOption) (*ProductCodes, error)
	FindAllStartingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error)
	StreamAllStartingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (Api_StreamAllStartingPromotionsClient, error)
	FindAllEndingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error)
	StreamAllEndingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (Api_StreamAllEndingPromotionsClient, error)
	ImportPromotion(ctx context.Context, in *ImportPromotionMultiRequest, opts ...grpc.CallOption) (*ImportPromotionResponse, error)
	StreamImportPromotion(ctx context.Context, opts ...grpc.CallOption) (Api_StreamImportPromotionClient, error)
	DeletePromotions(ctx context.Context, in *PromotionCodes, opts ...grpc.CallOption) (*OK, error)
	GetPromotionDetailByPromotionCode(ctx context.Context, in *PromotionDetailByPromotionCodeRequest, opts ...grpc.CallOption) (Api_GetPromotionDetailByPromotionCodeClient, error)
	// Promotion Channel API
	GetPromotionChannel(ctx context.Context, in *PromotionChannelRequest, opts ...grpc.CallOption) (*PromotionChannelResponse, error)
	// Promotion Source API
	GetPromotionSource(ctx context.Context, in *PromotionSourceRequest, opts ...grpc.CallOption) (*PromotionSourceResponse, error)
	DeletePromotionBasedOnSKU(ctx context.Context, opts ...grpc.CallOption) (Api_DeletePromotionBasedOnSKUClient, error)
	// Promotion reindex boosting
	StreamActivePromotedStockItems(ctx context.Context, in *PromotedStockItemRequest, opts ...grpc.CallOption) (Api_StreamActivePromotedStockItemsClient, error)
	StreamInactivePromotedStockItems(ctx context.Context, in *PromotedStockItemRequest, opts ...grpc.CallOption) (Api_StreamInactivePromotedStockItemsClient, error)
	Ping(ctx context.Context, in *Null, opts ...grpc.CallOption) (*OK, error)
	// Promotion Quota
	ImportPromotionQuota(ctx context.Context, in *ImportPromotionQuotaRequestMultiple, opts ...grpc.CallOption) (*ImportPromotionQuotaResponse, error)
	StreamImportPromotionQuota(ctx context.Context, opts ...grpc.CallOption) (Api_StreamImportPromotionQuotaClient, error)
	GetPromotionQuotaDetailInfoWithFilter(ctx context.Context, in *ImportPromotionQuotaRequest, opts ...grpc.CallOption) (*PromotionQuotaDetailInfoWithFilterMultiResponse, error)
	// Exclusive Promo API
	GetExclusivePromotion(ctx context.Context, in *ExclusivePromoRequest, opts ...grpc.CallOption) (*ProductCodes, error)
}

type apiClient struct {
	cc *grpc.ClientConn
}

func NewApiClient(cc *grpc.ClientConn) ApiClient {
	return &apiClient{cc}
}

func (c *apiClient) GetAllStockItemPromotion(ctx context.Context, in *StockItemPromotionMultiRequest, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error) {
	out := new(StockItemPromotionMultiResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetAllStockItemPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetAllStockItemPromotionChannel(ctx context.Context, in *StockItemPromotionMultiRequest, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error) {
	out := new(StockItemPromotionMultiResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetAllStockItemPromotionChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetPromotionDetailInfoWithFilter(ctx context.Context, in *PromotionDetailInfoWithFilterRequest, opts ...grpc.CallOption) (*PromotionDetailInfoWithFilterMultiResponse, error) {
	out := new(PromotionDetailInfoWithFilterMultiResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetPromotionDetailInfoWithFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetPromotedProductCodes(ctx context.Context, in *StoreIDDateChannel, opts ...grpc.CallOption) (*ProductCodes, error) {
	out := new(ProductCodes)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetPromotedProductCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) FindAllStartingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error) {
	out := new(StockItemPromotionMultiResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/FindAllStartingPromotions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StreamAllStartingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (Api_StreamAllStartingPromotionsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[0], "/aloha.promo.Api/StreamAllStartingPromotions", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiStreamAllStartingPromotionsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Api_StreamAllStartingPromotionsClient interface {
	Recv() (*StockItemPromotionResponse, error)
	grpc.ClientStream
}

type apiStreamAllStartingPromotionsClient struct {
	grpc.ClientStream
}

func (x *apiStreamAllStartingPromotionsClient) Recv() (*StockItemPromotionResponse, error) {
	m := new(StockItemPromotionResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) FindAllEndingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (*StockItemPromotionMultiResponse, error) {
	out := new(StockItemPromotionMultiResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/FindAllEndingPromotions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StreamAllEndingPromotions(ctx context.Context, in *DatesAndStoreIDs, opts ...grpc.CallOption) (Api_StreamAllEndingPromotionsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[1], "/aloha.promo.Api/StreamAllEndingPromotions", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiStreamAllEndingPromotionsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Api_StreamAllEndingPromotionsClient interface {
	Recv() (*StockItemPromotionResponse, error)
	grpc.ClientStream
}

type apiStreamAllEndingPromotionsClient struct {
	grpc.ClientStream
}

func (x *apiStreamAllEndingPromotionsClient) Recv() (*StockItemPromotionResponse, error) {
	m := new(StockItemPromotionResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) ImportPromotion(ctx context.Context, in *ImportPromotionMultiRequest, opts ...grpc.CallOption) (*ImportPromotionResponse, error) {
	out := new(ImportPromotionResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/ImportPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StreamImportPromotion(ctx context.Context, opts ...grpc.CallOption) (Api_StreamImportPromotionClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[2], "/aloha.promo.Api/StreamImportPromotion", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiStreamImportPromotionClient{stream}
	return x, nil
}

type Api_StreamImportPromotionClient interface {
	Send(*ImportPromotionDetailRequest) error
	CloseAndRecv() (*ImportPromotionResponse, error)
	grpc.ClientStream
}

type apiStreamImportPromotionClient struct {
	grpc.ClientStream
}

func (x *apiStreamImportPromotionClient) Send(m *ImportPromotionDetailRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *apiStreamImportPromotionClient) CloseAndRecv() (*ImportPromotionResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(ImportPromotionResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) DeletePromotions(ctx context.Context, in *PromotionCodes, opts ...grpc.CallOption) (*OK, error) {
	out := new(OK)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/DeletePromotions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetPromotionDetailByPromotionCode(ctx context.Context, in *PromotionDetailByPromotionCodeRequest, opts ...grpc.CallOption) (Api_GetPromotionDetailByPromotionCodeClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[3], "/aloha.promo.Api/GetPromotionDetailByPromotionCode", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiGetPromotionDetailByPromotionCodeClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Api_GetPromotionDetailByPromotionCodeClient interface {
	Recv() (*StockItemPromotionResponse, error)
	grpc.ClientStream
}

type apiGetPromotionDetailByPromotionCodeClient struct {
	grpc.ClientStream
}

func (x *apiGetPromotionDetailByPromotionCodeClient) Recv() (*StockItemPromotionResponse, error) {
	m := new(StockItemPromotionResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) GetPromotionChannel(ctx context.Context, in *PromotionChannelRequest, opts ...grpc.CallOption) (*PromotionChannelResponse, error) {
	out := new(PromotionChannelResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetPromotionChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetPromotionSource(ctx context.Context, in *PromotionSourceRequest, opts ...grpc.CallOption) (*PromotionSourceResponse, error) {
	out := new(PromotionSourceResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetPromotionSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) DeletePromotionBasedOnSKU(ctx context.Context, opts ...grpc.CallOption) (Api_DeletePromotionBasedOnSKUClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[4], "/aloha.promo.Api/DeletePromotionBasedOnSKU", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiDeletePromotionBasedOnSKUClient{stream}
	return x, nil
}

type Api_DeletePromotionBasedOnSKUClient interface {
	Send(*DeletePromotionDetailsRequest) error
	CloseAndRecv() (*DeletePromotionDetailsResponse, error)
	grpc.ClientStream
}

type apiDeletePromotionBasedOnSKUClient struct {
	grpc.ClientStream
}

func (x *apiDeletePromotionBasedOnSKUClient) Send(m *DeletePromotionDetailsRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *apiDeletePromotionBasedOnSKUClient) CloseAndRecv() (*DeletePromotionDetailsResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(DeletePromotionDetailsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) StreamActivePromotedStockItems(ctx context.Context, in *PromotedStockItemRequest, opts ...grpc.CallOption) (Api_StreamActivePromotedStockItemsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[5], "/aloha.promo.Api/StreamActivePromotedStockItems", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiStreamActivePromotedStockItemsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Api_StreamActivePromotedStockItemsClient interface {
	Recv() (*StockItemPriceAllChannelAndPromotion, error)
	grpc.ClientStream
}

type apiStreamActivePromotedStockItemsClient struct {
	grpc.ClientStream
}

func (x *apiStreamActivePromotedStockItemsClient) Recv() (*StockItemPriceAllChannelAndPromotion, error) {
	m := new(StockItemPriceAllChannelAndPromotion)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) StreamInactivePromotedStockItems(ctx context.Context, in *PromotedStockItemRequest, opts ...grpc.CallOption) (Api_StreamInactivePromotedStockItemsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[6], "/aloha.promo.Api/StreamInactivePromotedStockItems", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiStreamInactivePromotedStockItemsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Api_StreamInactivePromotedStockItemsClient interface {
	Recv() (*StockItemPriceAllChannelAndPromotion, error)
	grpc.ClientStream
}

type apiStreamInactivePromotedStockItemsClient struct {
	grpc.ClientStream
}

func (x *apiStreamInactivePromotedStockItemsClient) Recv() (*StockItemPriceAllChannelAndPromotion, error) {
	m := new(StockItemPriceAllChannelAndPromotion)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) Ping(ctx context.Context, in *Null, opts ...grpc.CallOption) (*OK, error) {
	out := new(OK)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/Ping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) ImportPromotionQuota(ctx context.Context, in *ImportPromotionQuotaRequestMultiple, opts ...grpc.CallOption) (*ImportPromotionQuotaResponse, error) {
	out := new(ImportPromotionQuotaResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/ImportPromotionQuota", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) StreamImportPromotionQuota(ctx context.Context, opts ...grpc.CallOption) (Api_StreamImportPromotionQuotaClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Api_serviceDesc.Streams[7], "/aloha.promo.Api/StreamImportPromotionQuota", opts...)
	if err != nil {
		return nil, err
	}
	x := &apiStreamImportPromotionQuotaClient{stream}
	return x, nil
}

type Api_StreamImportPromotionQuotaClient interface {
	Send(*ImportPromotionQuotaRequest) error
	CloseAndRecv() (*ImportPromotionQuotaResponse, error)
	grpc.ClientStream
}

type apiStreamImportPromotionQuotaClient struct {
	grpc.ClientStream
}

func (x *apiStreamImportPromotionQuotaClient) Send(m *ImportPromotionQuotaRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *apiStreamImportPromotionQuotaClient) CloseAndRecv() (*ImportPromotionQuotaResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(ImportPromotionQuotaResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *apiClient) GetPromotionQuotaDetailInfoWithFilter(ctx context.Context, in *ImportPromotionQuotaRequest, opts ...grpc.CallOption) (*PromotionQuotaDetailInfoWithFilterMultiResponse, error) {
	out := new(PromotionQuotaDetailInfoWithFilterMultiResponse)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetPromotionQuotaDetailInfoWithFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) GetExclusivePromotion(ctx context.Context, in *ExclusivePromoRequest, opts ...grpc.CallOption) (*ProductCodes, error) {
	out := new(ProductCodes)
	err := c.cc.Invoke(ctx, "/aloha.promo.Api/GetExclusivePromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiServer is the server API for Api service.
type ApiServer interface {
	// Promotion API
	GetAllStockItemPromotion(context.Context, *StockItemPromotionMultiRequest) (*StockItemPromotionMultiResponse, error)
	GetAllStockItemPromotionChannel(context.Context, *StockItemPromotionMultiRequest) (*StockItemPromotionMultiResponse, error)
	GetPromotionDetailInfoWithFilter(context.Context, *PromotionDetailInfoWithFilterRequest) (*PromotionDetailInfoWithFilterMultiResponse, error)
	GetPromotedProductCodes(context.Context, *StoreIDDateChannel) (*ProductCodes, error)
	FindAllStartingPromotions(context.Context, *DatesAndStoreIDs) (*StockItemPromotionMultiResponse, error)
	StreamAllStartingPromotions(*DatesAndStoreIDs, Api_StreamAllStartingPromotionsServer) error
	FindAllEndingPromotions(context.Context, *DatesAndStoreIDs) (*StockItemPromotionMultiResponse, error)
	StreamAllEndingPromotions(*DatesAndStoreIDs, Api_StreamAllEndingPromotionsServer) error
	ImportPromotion(context.Context, *ImportPromotionMultiRequest) (*ImportPromotionResponse, error)
	StreamImportPromotion(Api_StreamImportPromotionServer) error
	DeletePromotions(context.Context, *PromotionCodes) (*OK, error)
	GetPromotionDetailByPromotionCode(*PromotionDetailByPromotionCodeRequest, Api_GetPromotionDetailByPromotionCodeServer) error
	// Promotion Channel API
	GetPromotionChannel(context.Context, *PromotionChannelRequest) (*PromotionChannelResponse, error)
	// Promotion Source API
	GetPromotionSource(context.Context, *PromotionSourceRequest) (*PromotionSourceResponse, error)
	DeletePromotionBasedOnSKU(Api_DeletePromotionBasedOnSKUServer) error
	// Promotion reindex boosting
	StreamActivePromotedStockItems(*PromotedStockItemRequest, Api_StreamActivePromotedStockItemsServer) error
	StreamInactivePromotedStockItems(*PromotedStockItemRequest, Api_StreamInactivePromotedStockItemsServer) error
	Ping(context.Context, *Null) (*OK, error)
	// Promotion Quota
	ImportPromotionQuota(context.Context, *ImportPromotionQuotaRequestMultiple) (*ImportPromotionQuotaResponse, error)
	StreamImportPromotionQuota(Api_StreamImportPromotionQuotaServer) error
	GetPromotionQuotaDetailInfoWithFilter(context.Context, *ImportPromotionQuotaRequest) (*PromotionQuotaDetailInfoWithFilterMultiResponse, error)
	// Exclusive Promo API
	GetExclusivePromotion(context.Context, *ExclusivePromoRequest) (*ProductCodes, error)
}

// UnimplementedApiServer can be embedded to have forward compatible implementations.
type UnimplementedApiServer struct {
}

func (*UnimplementedApiServer) GetAllStockItemPromotion(ctx context.Context, req *StockItemPromotionMultiRequest) (*StockItemPromotionMultiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllStockItemPromotion not implemented")
}
func (*UnimplementedApiServer) GetAllStockItemPromotionChannel(ctx context.Context, req *StockItemPromotionMultiRequest) (*StockItemPromotionMultiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllStockItemPromotionChannel not implemented")
}
func (*UnimplementedApiServer) GetPromotionDetailInfoWithFilter(ctx context.Context, req *PromotionDetailInfoWithFilterRequest) (*PromotionDetailInfoWithFilterMultiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPromotionDetailInfoWithFilter not implemented")
}
func (*UnimplementedApiServer) GetPromotedProductCodes(ctx context.Context, req *StoreIDDateChannel) (*ProductCodes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPromotedProductCodes not implemented")
}
func (*UnimplementedApiServer) FindAllStartingPromotions(ctx context.Context, req *DatesAndStoreIDs) (*StockItemPromotionMultiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllStartingPromotions not implemented")
}
func (*UnimplementedApiServer) StreamAllStartingPromotions(req *DatesAndStoreIDs, srv Api_StreamAllStartingPromotionsServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamAllStartingPromotions not implemented")
}
func (*UnimplementedApiServer) FindAllEndingPromotions(ctx context.Context, req *DatesAndStoreIDs) (*StockItemPromotionMultiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAllEndingPromotions not implemented")
}
func (*UnimplementedApiServer) StreamAllEndingPromotions(req *DatesAndStoreIDs, srv Api_StreamAllEndingPromotionsServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamAllEndingPromotions not implemented")
}
func (*UnimplementedApiServer) ImportPromotion(ctx context.Context, req *ImportPromotionMultiRequest) (*ImportPromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportPromotion not implemented")
}
func (*UnimplementedApiServer) StreamImportPromotion(srv Api_StreamImportPromotionServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamImportPromotion not implemented")
}
func (*UnimplementedApiServer) DeletePromotions(ctx context.Context, req *PromotionCodes) (*OK, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePromotions not implemented")
}
func (*UnimplementedApiServer) GetPromotionDetailByPromotionCode(req *PromotionDetailByPromotionCodeRequest, srv Api_GetPromotionDetailByPromotionCodeServer) error {
	return status.Errorf(codes.Unimplemented, "method GetPromotionDetailByPromotionCode not implemented")
}
func (*UnimplementedApiServer) GetPromotionChannel(ctx context.Context, req *PromotionChannelRequest) (*PromotionChannelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPromotionChannel not implemented")
}
func (*UnimplementedApiServer) GetPromotionSource(ctx context.Context, req *PromotionSourceRequest) (*PromotionSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPromotionSource not implemented")
}
func (*UnimplementedApiServer) DeletePromotionBasedOnSKU(srv Api_DeletePromotionBasedOnSKUServer) error {
	return status.Errorf(codes.Unimplemented, "method DeletePromotionBasedOnSKU not implemented")
}
func (*UnimplementedApiServer) StreamActivePromotedStockItems(req *PromotedStockItemRequest, srv Api_StreamActivePromotedStockItemsServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamActivePromotedStockItems not implemented")
}
func (*UnimplementedApiServer) StreamInactivePromotedStockItems(req *PromotedStockItemRequest, srv Api_StreamInactivePromotedStockItemsServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamInactivePromotedStockItems not implemented")
}
func (*UnimplementedApiServer) Ping(ctx context.Context, req *Null) (*OK, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (*UnimplementedApiServer) ImportPromotionQuota(ctx context.Context, req *ImportPromotionQuotaRequestMultiple) (*ImportPromotionQuotaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportPromotionQuota not implemented")
}
func (*UnimplementedApiServer) StreamImportPromotionQuota(srv Api_StreamImportPromotionQuotaServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamImportPromotionQuota not implemented")
}
func (*UnimplementedApiServer) GetPromotionQuotaDetailInfoWithFilter(ctx context.Context, req *ImportPromotionQuotaRequest) (*PromotionQuotaDetailInfoWithFilterMultiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPromotionQuotaDetailInfoWithFilter not implemented")
}
func (*UnimplementedApiServer) GetExclusivePromotion(ctx context.Context, req *ExclusivePromoRequest) (*ProductCodes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExclusivePromotion not implemented")
}

func RegisterApiServer(s *grpc.Server, srv ApiServer) {
	s.RegisterService(&_Api_serviceDesc, srv)
}

func _Api_GetAllStockItemPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StockItemPromotionMultiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetAllStockItemPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetAllStockItemPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetAllStockItemPromotion(ctx, req.(*StockItemPromotionMultiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetAllStockItemPromotionChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StockItemPromotionMultiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetAllStockItemPromotionChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetAllStockItemPromotionChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetAllStockItemPromotionChannel(ctx, req.(*StockItemPromotionMultiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetPromotionDetailInfoWithFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionDetailInfoWithFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPromotionDetailInfoWithFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetPromotionDetailInfoWithFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPromotionDetailInfoWithFilter(ctx, req.(*PromotionDetailInfoWithFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetPromotedProductCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreIDDateChannel)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPromotedProductCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetPromotedProductCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPromotedProductCodes(ctx, req.(*StoreIDDateChannel))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_FindAllStartingPromotions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DatesAndStoreIDs)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).FindAllStartingPromotions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/FindAllStartingPromotions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).FindAllStartingPromotions(ctx, req.(*DatesAndStoreIDs))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StreamAllStartingPromotions_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DatesAndStoreIDs)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApiServer).StreamAllStartingPromotions(m, &apiStreamAllStartingPromotionsServer{stream})
}

type Api_StreamAllStartingPromotionsServer interface {
	Send(*StockItemPromotionResponse) error
	grpc.ServerStream
}

type apiStreamAllStartingPromotionsServer struct {
	grpc.ServerStream
}

func (x *apiStreamAllStartingPromotionsServer) Send(m *StockItemPromotionResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Api_FindAllEndingPromotions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DatesAndStoreIDs)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).FindAllEndingPromotions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/FindAllEndingPromotions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).FindAllEndingPromotions(ctx, req.(*DatesAndStoreIDs))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StreamAllEndingPromotions_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DatesAndStoreIDs)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApiServer).StreamAllEndingPromotions(m, &apiStreamAllEndingPromotionsServer{stream})
}

type Api_StreamAllEndingPromotionsServer interface {
	Send(*StockItemPromotionResponse) error
	grpc.ServerStream
}

type apiStreamAllEndingPromotionsServer struct {
	grpc.ServerStream
}

func (x *apiStreamAllEndingPromotionsServer) Send(m *StockItemPromotionResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Api_ImportPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportPromotionMultiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ImportPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/ImportPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ImportPromotion(ctx, req.(*ImportPromotionMultiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StreamImportPromotion_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ApiServer).StreamImportPromotion(&apiStreamImportPromotionServer{stream})
}

type Api_StreamImportPromotionServer interface {
	SendAndClose(*ImportPromotionResponse) error
	Recv() (*ImportPromotionDetailRequest, error)
	grpc.ServerStream
}

type apiStreamImportPromotionServer struct {
	grpc.ServerStream
}

func (x *apiStreamImportPromotionServer) SendAndClose(m *ImportPromotionResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *apiStreamImportPromotionServer) Recv() (*ImportPromotionDetailRequest, error) {
	m := new(ImportPromotionDetailRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Api_DeletePromotions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionCodes)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).DeletePromotions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/DeletePromotions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).DeletePromotions(ctx, req.(*PromotionCodes))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetPromotionDetailByPromotionCode_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(PromotionDetailByPromotionCodeRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApiServer).GetPromotionDetailByPromotionCode(m, &apiGetPromotionDetailByPromotionCodeServer{stream})
}

type Api_GetPromotionDetailByPromotionCodeServer interface {
	Send(*StockItemPromotionResponse) error
	grpc.ServerStream
}

type apiGetPromotionDetailByPromotionCodeServer struct {
	grpc.ServerStream
}

func (x *apiGetPromotionDetailByPromotionCodeServer) Send(m *StockItemPromotionResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Api_GetPromotionChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPromotionChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetPromotionChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPromotionChannel(ctx, req.(*PromotionChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetPromotionSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PromotionSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPromotionSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetPromotionSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPromotionSource(ctx, req.(*PromotionSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_DeletePromotionBasedOnSKU_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ApiServer).DeletePromotionBasedOnSKU(&apiDeletePromotionBasedOnSKUServer{stream})
}

type Api_DeletePromotionBasedOnSKUServer interface {
	SendAndClose(*DeletePromotionDetailsResponse) error
	Recv() (*DeletePromotionDetailsRequest, error)
	grpc.ServerStream
}

type apiDeletePromotionBasedOnSKUServer struct {
	grpc.ServerStream
}

func (x *apiDeletePromotionBasedOnSKUServer) SendAndClose(m *DeletePromotionDetailsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *apiDeletePromotionBasedOnSKUServer) Recv() (*DeletePromotionDetailsRequest, error) {
	m := new(DeletePromotionDetailsRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Api_StreamActivePromotedStockItems_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(PromotedStockItemRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApiServer).StreamActivePromotedStockItems(m, &apiStreamActivePromotedStockItemsServer{stream})
}

type Api_StreamActivePromotedStockItemsServer interface {
	Send(*StockItemPriceAllChannelAndPromotion) error
	grpc.ServerStream
}

type apiStreamActivePromotedStockItemsServer struct {
	grpc.ServerStream
}

func (x *apiStreamActivePromotedStockItemsServer) Send(m *StockItemPriceAllChannelAndPromotion) error {
	return x.ServerStream.SendMsg(m)
}

func _Api_StreamInactivePromotedStockItems_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(PromotedStockItemRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ApiServer).StreamInactivePromotedStockItems(m, &apiStreamInactivePromotedStockItemsServer{stream})
}

type Api_StreamInactivePromotedStockItemsServer interface {
	Send(*StockItemPriceAllChannelAndPromotion) error
	grpc.ServerStream
}

type apiStreamInactivePromotedStockItemsServer struct {
	grpc.ServerStream
}

func (x *apiStreamInactivePromotedStockItemsServer) Send(m *StockItemPriceAllChannelAndPromotion) error {
	return x.ServerStream.SendMsg(m)
}

func _Api_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Null)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/Ping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).Ping(ctx, req.(*Null))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_ImportPromotionQuota_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportPromotionQuotaRequestMultiple)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ImportPromotionQuota(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/ImportPromotionQuota",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ImportPromotionQuota(ctx, req.(*ImportPromotionQuotaRequestMultiple))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_StreamImportPromotionQuota_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ApiServer).StreamImportPromotionQuota(&apiStreamImportPromotionQuotaServer{stream})
}

type Api_StreamImportPromotionQuotaServer interface {
	SendAndClose(*ImportPromotionQuotaResponse) error
	Recv() (*ImportPromotionQuotaRequest, error)
	grpc.ServerStream
}

type apiStreamImportPromotionQuotaServer struct {
	grpc.ServerStream
}

func (x *apiStreamImportPromotionQuotaServer) SendAndClose(m *ImportPromotionQuotaResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *apiStreamImportPromotionQuotaServer) Recv() (*ImportPromotionQuotaRequest, error) {
	m := new(ImportPromotionQuotaRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Api_GetPromotionQuotaDetailInfoWithFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportPromotionQuotaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetPromotionQuotaDetailInfoWithFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetPromotionQuotaDetailInfoWithFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetPromotionQuotaDetailInfoWithFilter(ctx, req.(*ImportPromotionQuotaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_GetExclusivePromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExclusivePromoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GetExclusivePromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aloha.promo.Api/GetExclusivePromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GetExclusivePromotion(ctx, req.(*ExclusivePromoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Api_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aloha.promo.Api",
	HandlerType: (*ApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAllStockItemPromotion",
			Handler:    _Api_GetAllStockItemPromotion_Handler,
		},
		{
			MethodName: "GetAllStockItemPromotionChannel",
			Handler:    _Api_GetAllStockItemPromotionChannel_Handler,
		},
		{
			MethodName: "GetPromotionDetailInfoWithFilter",
			Handler:    _Api_GetPromotionDetailInfoWithFilter_Handler,
		},
		{
			MethodName: "GetPromotedProductCodes",
			Handler:    _Api_GetPromotedProductCodes_Handler,
		},
		{
			MethodName: "FindAllStartingPromotions",
			Handler:    _Api_FindAllStartingPromotions_Handler,
		},
		{
			MethodName: "FindAllEndingPromotions",
			Handler:    _Api_FindAllEndingPromotions_Handler,
		},
		{
			MethodName: "ImportPromotion",
			Handler:    _Api_ImportPromotion_Handler,
		},
		{
			MethodName: "DeletePromotions",
			Handler:    _Api_DeletePromotions_Handler,
		},
		{
			MethodName: "GetPromotionChannel",
			Handler:    _Api_GetPromotionChannel_Handler,
		},
		{
			MethodName: "GetPromotionSource",
			Handler:    _Api_GetPromotionSource_Handler,
		},
		{
			MethodName: "Ping",
			Handler:    _Api_Ping_Handler,
		},
		{
			MethodName: "ImportPromotionQuota",
			Handler:    _Api_ImportPromotionQuota_Handler,
		},
		{
			MethodName: "GetPromotionQuotaDetailInfoWithFilter",
			Handler:    _Api_GetPromotionQuotaDetailInfoWithFilter_Handler,
		},
		{
			MethodName: "GetExclusivePromotion",
			Handler:    _Api_GetExclusivePromotion_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamAllStartingPromotions",
			Handler:       _Api_StreamAllStartingPromotions_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "StreamAllEndingPromotions",
			Handler:       _Api_StreamAllEndingPromotions_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "StreamImportPromotion",
			Handler:       _Api_StreamImportPromotion_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "GetPromotionDetailByPromotionCode",
			Handler:       _Api_GetPromotionDetailByPromotionCode_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "DeletePromotionBasedOnSKU",
			Handler:       _Api_DeletePromotionBasedOnSKU_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "StreamActivePromotedStockItems",
			Handler:       _Api_StreamActivePromotedStockItems_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "StreamInactivePromotedStockItems",
			Handler:       _Api_StreamInactivePromotedStockItems_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "StreamImportPromotionQuota",
			Handler:       _Api_StreamImportPromotionQuota_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "promotion.proto",
}

func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Count != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.Count))
		i--
		dAtA[i] = 0x10
	}
	if len(m.PromotionQuotaDetailInfo) > 0 {
		for iNdEx := len(m.PromotionQuotaDetailInfo) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.PromotionQuotaDetailInfo[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.CreatedAt) > 0 {
		i -= len(m.CreatedAt)
		copy(dAtA[i:], m.CreatedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.CreatedAt)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Active {
		i--
		if m.Active {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.Quota != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.Quota))
		i--
		dAtA[i] = 0x18
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionQuotaRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionQuotaRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionQuotaRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Active {
		i--
		if m.Active {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.Quota != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.Quota))
		i--
		dAtA[i] = 0x18
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionQuotaRequestMultiple) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionQuotaRequestMultiple) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionQuotaRequestMultiple) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ImportPromotionQuotaRequest) > 0 {
		for iNdEx := len(m.ImportPromotionQuotaRequest) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ImportPromotionQuotaRequest[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionQuotaResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionQuotaResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionQuotaResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ImportPromotionQuotaError) > 0 {
		for iNdEx := len(m.ImportPromotionQuotaError) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ImportPromotionQuotaError[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionQuotaError) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionQuotaError) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionQuotaError) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Errors) > 0 {
		for iNdEx := len(m.Errors) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Errors[iNdEx])
			copy(dAtA[i:], m.Errors[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.Errors[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionDetail) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionDetail) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Actions) > 0 {
		for k := range m.Actions {
			v := m.Actions[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Rules) > 0 {
		for k := range m.Rules {
			v := m.Rules[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0x22
	}
	if m.ChannelId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.ChannelId))
		i--
		dAtA[i] = 0x18
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionDetailRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionDetailRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionDetailRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Actions) > 0 {
		for k := range m.Actions {
			v := m.Actions[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x52
		}
	}
	if len(m.Rules) > 0 {
		for k := range m.Rules {
			v := m.Rules[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x4a
		}
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0x42
	}
	if m.ChannelId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.ChannelId))
		i--
		dAtA[i] = 0x38
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x30
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.LastDate) > 0 {
		i -= len(m.LastDate)
		copy(dAtA[i:], m.LastDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.LastDate)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.StartDate) > 0 {
		i -= len(m.StartDate)
		copy(dAtA[i:], m.StartDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.StartDate)))
		i--
		dAtA[i] = 0x1a
	}
	if m.SourceId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.SourceId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PromotionChannelRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionChannelRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionChannelRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *PromotionChannelResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionChannelResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionChannelResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.PromotionChannelInfo) > 0 {
		for k := range m.PromotionChannelInfo {
			v := m.PromotionChannelInfo[k]
			baseI := i
			i = encodeVarintPromotion(dAtA, i, uint64(v))
			i--
			dAtA[i] = 0x10
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *StockItemPromotionMultiRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StockItemPromotionMultiRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StockItemPromotionMultiRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.CountryCode) > 0 {
		i -= len(m.CountryCode)
		copy(dAtA[i:], m.CountryCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.CountryCode)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Channel) > 0 {
		i -= len(m.Channel)
		copy(dAtA[i:], m.Channel)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Channel)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.StoreIds) > 0 {
		for iNdEx := len(m.StoreIds) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.StoreIds[iNdEx])
			copy(dAtA[i:], m.StoreIds[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.StoreIds[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ProductCodes) > 0 {
		for iNdEx := len(m.ProductCodes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ProductCodes[iNdEx])
			copy(dAtA[i:], m.ProductCodes[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCodes[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *StockItemPromotionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StockItemPromotionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StockItemPromotionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.SourceId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.SourceId))
		i--
		dAtA[i] = 0x58
	}
	if len(m.DeletedAt) > 0 {
		i -= len(m.DeletedAt)
		copy(dAtA[i:], m.DeletedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.DeletedAt)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.Actions) > 0 {
		for k := range m.Actions {
			v := m.Actions[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x4a
		}
	}
	if len(m.Rules) > 0 {
		for k := range m.Rules {
			v := m.Rules[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.Channel) > 0 {
		i -= len(m.Channel)
		copy(dAtA[i:], m.Channel)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Channel)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0x32
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.LastDate) > 0 {
		i -= len(m.LastDate)
		copy(dAtA[i:], m.LastDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.LastDate)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.StartDate) > 0 {
		i -= len(m.StartDate)
		copy(dAtA[i:], m.StartDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.StartDate)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StockItemPromotionMultiResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StockItemPromotionMultiResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StockItemPromotionMultiResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.StockItemPromotions) > 0 {
		for iNdEx := len(m.StockItemPromotions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.StockItemPromotions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ProductCodes) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProductCodes) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ProductCodes) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ProductCodes) > 0 {
		for iNdEx := len(m.ProductCodes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ProductCodes[iNdEx])
			copy(dAtA[i:], m.ProductCodes[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCodes[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.CreatedAt) > 0 {
		i -= len(m.CreatedAt)
		copy(dAtA[i:], m.CreatedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.CreatedAt)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.LastDate) > 0 {
		i -= len(m.LastDate)
		copy(dAtA[i:], m.LastDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.LastDate)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.StartDate) > 0 {
		i -= len(m.StartDate)
		copy(dAtA[i:], m.StartDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.StartDate)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.PromotionDetails) > 0 {
		for iNdEx := len(m.PromotionDetails) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.PromotionDetails[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.SourceId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.SourceId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionMultiRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionMultiRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionMultiRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ImportPromotionRequest) > 0 {
		for iNdEx := len(m.ImportPromotionRequest) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ImportPromotionRequest[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ImportPromotionError) > 0 {
		for iNdEx := len(m.ImportPromotionError) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ImportPromotionError[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ImportPromotionError) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportPromotionError) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportPromotionError) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Errors) > 0 {
		for iNdEx := len(m.Errors) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Errors[iNdEx])
			copy(dAtA[i:], m.Errors[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.Errors[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.ChannelId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.ChannelId))
		i--
		dAtA[i] = 0x20
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x18
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PromotionSource) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionSource) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionSource) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if m.Id != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PromotionSourceRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionSourceRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionSourceRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *PromotionSourceResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionSourceResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionSourceResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.PromotionSource) > 0 {
		for iNdEx := len(m.PromotionSource) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.PromotionSource[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *PromotionDetailInfoWithFilterRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionDetailInfoWithFilterRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionDetailInfoWithFilterRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.From != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.From))
		i--
		dAtA[i] = 0x40
	}
	if m.Size_ != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.Size_))
		i--
		dAtA[i] = 0x38
	}
	if m.SourceId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.SourceId))
		i--
		dAtA[i] = 0x30
	}
	if len(m.LastDate) > 0 {
		i -= len(m.LastDate)
		copy(dAtA[i:], m.LastDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.LastDate)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.StartDate) > 0 {
		i -= len(m.StartDate)
		copy(dAtA[i:], m.StartDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.StartDate)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.StoreIds) > 0 {
		dAtA2 := make([]byte, len(m.StoreIds)*10)
		var j1 int
		for _, num1 := range m.StoreIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		i -= j1
		copy(dAtA[i:], dAtA2[:j1])
		i = encodeVarintPromotion(dAtA, i, uint64(j1))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PromotionDetailInfoWithFilterResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionDetailInfoWithFilterResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionDetailInfoWithFilterResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.PromotionDetailsDeletedAt) > 0 {
		i -= len(m.PromotionDetailsDeletedAt)
		copy(dAtA[i:], m.PromotionDetailsDeletedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionDetailsDeletedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.PromotionsDeletedAt) > 0 {
		i -= len(m.PromotionsDeletedAt)
		copy(dAtA[i:], m.PromotionsDeletedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionsDeletedAt)))
		i--
		dAtA[i] = 0x6a
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.CreatedAt) > 0 {
		i -= len(m.CreatedAt)
		copy(dAtA[i:], m.CreatedAt)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.CreatedAt)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.LastDate) > 0 {
		i -= len(m.LastDate)
		copy(dAtA[i:], m.LastDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.LastDate)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.StartDate) > 0 {
		i -= len(m.StartDate)
		copy(dAtA[i:], m.StartDate)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.StartDate)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Source) > 0 {
		i -= len(m.Source)
		copy(dAtA[i:], m.Source)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Source)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Channel) > 0 {
		i -= len(m.Channel)
		copy(dAtA[i:], m.Channel)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Channel)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Actions) > 0 {
		for k := range m.Actions {
			v := m.Actions[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Rules) > 0 {
		for k := range m.Rules {
			v := m.Rules[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x22
		}
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x18
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PromotionDetailInfoWithFilterMultiResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionDetailInfoWithFilterMultiResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionDetailInfoWithFilterMultiResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Count != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.Count))
		i--
		dAtA[i] = 0x10
	}
	if len(m.PromotionDetailInfo) > 0 {
		for iNdEx := len(m.PromotionDetailInfo) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.PromotionDetailInfo[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *PromotionCodes) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionCodes) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionCodes) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.PromotionCodes) > 0 {
		for iNdEx := len(m.PromotionCodes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.PromotionCodes[iNdEx])
			copy(dAtA[i:], m.PromotionCodes[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCodes[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DatesAndStoreIDs) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatesAndStoreIDs) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DatesAndStoreIDs) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Dates) > 0 {
		for iNdEx := len(m.Dates) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Dates[iNdEx])
			copy(dAtA[i:], m.Dates[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.Dates[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.StoreIds) > 0 {
		dAtA4 := make([]byte, len(m.StoreIds)*10)
		var j3 int
		for _, num1 := range m.StoreIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		i -= j3
		copy(dAtA[i:], dAtA4[:j3])
		i = encodeVarintPromotion(dAtA, i, uint64(j3))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StoreIDDateChannel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreIDDateChannel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreIDDateChannel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Channel) > 0 {
		i -= len(m.Channel)
		copy(dAtA[i:], m.Channel)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Channel)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Date) > 0 {
		i -= len(m.Date)
		copy(dAtA[i:], m.Date)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Date)))
		i--
		dAtA[i] = 0x12
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DeletePromotionDetailsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeletePromotionDetailsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeletePromotionDetailsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Channels) > 0 {
		for iNdEx := len(m.Channels) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Channels[iNdEx])
			copy(dAtA[i:], m.Channels[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.Channels[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.PromotionCodes) > 0 {
		for iNdEx := len(m.PromotionCodes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.PromotionCodes[iNdEx])
			copy(dAtA[i:], m.PromotionCodes[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCodes[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.StoreIds) > 0 {
		dAtA6 := make([]byte, len(m.StoreIds)*10)
		var j5 int
		for _, num1 := range m.StoreIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA6[j5] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j5++
			}
			dAtA6[j5] = uint8(num)
			j5++
		}
		i -= j5
		copy(dAtA[i:], dAtA6[:j5])
		i = encodeVarintPromotion(dAtA, i, uint64(j5))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeletePromotionDetailsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeletePromotionDetailsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeletePromotionDetailsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Errors) > 0 {
		for iNdEx := len(m.Errors) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Errors[iNdEx])
			copy(dAtA[i:], m.Errors[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.Errors[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Ok {
		i--
		if m.Ok {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PromotionDetailByPromotionCodeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionDetailByPromotionCodeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionDetailByPromotionCodeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.PromotionCode) > 0 {
		i -= len(m.PromotionCode)
		copy(dAtA[i:], m.PromotionCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.PromotionCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StockItemPriceAllChannelAndPromotion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StockItemPriceAllChannelAndPromotion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StockItemPriceAllChannelAndPromotion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Promotions) > 0 {
		for iNdEx := len(m.Promotions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Promotions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintPromotion(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ProductCode) > 0 {
		i -= len(m.ProductCode)
		copy(dAtA[i:], m.ProductCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.ProductCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PromotionAllChannel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotionAllChannel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotionAllChannel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Actions) > 0 {
		for k := range m.Actions {
			v := m.Actions[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Rules) > 0 {
		for k := range m.Rules {
			v := m.Rules[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintPromotion(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintPromotion(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintPromotion(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x22
		}
	}
	if m.Price != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Price))))
		i--
		dAtA[i] = 0x19
	}
	if len(m.Channel) > 0 {
		i -= len(m.Channel)
		copy(dAtA[i:], m.Channel)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Channel)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Code) > 0 {
		i -= len(m.Code)
		copy(dAtA[i:], m.Code)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Code)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PromotedStockItemRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PromotedStockItemRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PromotedStockItemRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.CountryCode) > 0 {
		i -= len(m.CountryCode)
		copy(dAtA[i:], m.CountryCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.CountryCode)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.StoreIds) > 0 {
		dAtA8 := make([]byte, len(m.StoreIds)*10)
		var j7 int
		for _, num1 := range m.StoreIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA8[j7] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j7++
			}
			dAtA8[j7] = uint8(num)
			j7++
		}
		i -= j7
		copy(dAtA[i:], dAtA8[:j7])
		i = encodeVarintPromotion(dAtA, i, uint64(j7))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Dates) > 0 {
		for iNdEx := len(m.Dates) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Dates[iNdEx])
			copy(dAtA[i:], m.Dates[iNdEx])
			i = encodeVarintPromotion(dAtA, i, uint64(len(m.Dates[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *OK) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OK) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OK) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Ok {
		i--
		if m.Ok {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ExclusivePromoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExclusivePromoRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExclusivePromoRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Channel) > 0 {
		i -= len(m.Channel)
		copy(dAtA[i:], m.Channel)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.Channel)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.CountryCode) > 0 {
		i -= len(m.CountryCode)
		copy(dAtA[i:], m.CountryCode)
		i = encodeVarintPromotion(dAtA, i, uint64(len(m.CountryCode)))
		i--
		dAtA[i] = 0x12
	}
	if m.StoreId != 0 {
		i = encodeVarintPromotion(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Null) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Null) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Null) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func encodeVarintPromotion(dAtA []byte, offset int, v uint64) int {
	offset -= sovPromotion(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.PromotionQuotaDetailInfo) > 0 {
		for _, e := range m.PromotionQuotaDetailInfo {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.Count != 0 {
		n += 1 + sovPromotion(uint64(m.Count))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionQuotaDetailInfoWithFilterResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.Quota != 0 {
		n += 1 + sovPromotion(uint64(m.Quota))
	}
	if m.Active {
		n += 2
	}
	l = len(m.CreatedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionQuotaRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.Quota != 0 {
		n += 1 + sovPromotion(uint64(m.Quota))
	}
	if m.Active {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionQuotaRequestMultiple) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ImportPromotionQuotaRequest) > 0 {
		for _, e := range m.ImportPromotionQuotaRequest {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionQuotaResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ImportPromotionQuotaError) > 0 {
		for _, e := range m.ImportPromotionQuotaError {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionQuotaError) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if len(m.Errors) > 0 {
		for _, s := range m.Errors {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionDetail) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	if m.ChannelId != 0 {
		n += 1 + sovPromotion(uint64(m.ChannelId))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if len(m.Rules) > 0 {
		for k, v := range m.Rules {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if len(m.Actions) > 0 {
		for k, v := range m.Actions {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionDetailRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.SourceId != 0 {
		n += 1 + sovPromotion(uint64(m.SourceId))
	}
	l = len(m.StartDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.LastDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	if m.ChannelId != 0 {
		n += 1 + sovPromotion(uint64(m.ChannelId))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if len(m.Rules) > 0 {
		for k, v := range m.Rules {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if len(m.Actions) > 0 {
		for k, v := range m.Actions {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionChannelRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionChannelResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.PromotionChannelInfo) > 0 {
		for k, v := range m.PromotionChannelInfo {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + sovPromotion(uint64(v))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StockItemPromotionMultiRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ProductCodes) > 0 {
		for _, s := range m.ProductCodes {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if len(m.StoreIds) > 0 {
		for _, s := range m.StoreIds {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	l = len(m.Channel)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.CountryCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StockItemPromotionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.StartDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.LastDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.Channel)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if len(m.Rules) > 0 {
		for k, v := range m.Rules {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if len(m.Actions) > 0 {
		for k, v := range m.Actions {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	l = len(m.DeletedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.SourceId != 0 {
		n += 1 + sovPromotion(uint64(m.SourceId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StockItemPromotionMultiResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.StockItemPromotions) > 0 {
		for _, e := range m.StockItemPromotions {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ProductCodes) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ProductCodes) > 0 {
		for _, s := range m.ProductCodes {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.SourceId != 0 {
		n += 1 + sovPromotion(uint64(m.SourceId))
	}
	if len(m.PromotionDetails) > 0 {
		for _, e := range m.PromotionDetails {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	l = len(m.StartDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.LastDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.CreatedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionMultiRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ImportPromotionRequest) > 0 {
		for _, e := range m.ImportPromotionRequest {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ImportPromotionError) > 0 {
		for _, e := range m.ImportPromotionError {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ImportPromotionError) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	if m.ChannelId != 0 {
		n += 1 + sovPromotion(uint64(m.ChannelId))
	}
	if len(m.Errors) > 0 {
		for _, s := range m.Errors {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionSource) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovPromotion(uint64(m.Id))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionSourceRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionSourceResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.PromotionSource) > 0 {
		for _, e := range m.PromotionSource {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionDetailInfoWithFilterRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.StoreIds) > 0 {
		l = 0
		for _, e := range m.StoreIds {
			l += sovPromotion(uint64(e))
		}
		n += 1 + sovPromotion(uint64(l)) + l
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.StartDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.LastDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.SourceId != 0 {
		n += 1 + sovPromotion(uint64(m.SourceId))
	}
	if m.Size_ != 0 {
		n += 1 + sovPromotion(uint64(m.Size_))
	}
	if m.From != 0 {
		n += 1 + sovPromotion(uint64(m.From))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionDetailInfoWithFilterResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	if len(m.Rules) > 0 {
		for k, v := range m.Rules {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if len(m.Actions) > 0 {
		for k, v := range m.Actions {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	l = len(m.Channel)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.Source)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.StartDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.LastDate)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.CreatedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.PromotionsDeletedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.PromotionDetailsDeletedAt)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionDetailInfoWithFilterMultiResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.PromotionDetailInfo) > 0 {
		for _, e := range m.PromotionDetailInfo {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.Count != 0 {
		n += 1 + sovPromotion(uint64(m.Count))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionCodes) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.PromotionCodes) > 0 {
		for _, s := range m.PromotionCodes {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DatesAndStoreIDs) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.StoreIds) > 0 {
		l = 0
		for _, e := range m.StoreIds {
			l += sovPromotion(uint64(e))
		}
		n += 1 + sovPromotion(uint64(l)) + l
	}
	if len(m.Dates) > 0 {
		for _, s := range m.Dates {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreIDDateChannel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	l = len(m.Date)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.Channel)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeletePromotionDetailsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if len(m.StoreIds) > 0 {
		l = 0
		for _, e := range m.StoreIds {
			l += sovPromotion(uint64(e))
		}
		n += 1 + sovPromotion(uint64(l)) + l
	}
	if len(m.PromotionCodes) > 0 {
		for _, s := range m.PromotionCodes {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if len(m.Channels) > 0 {
		for _, s := range m.Channels {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeletePromotionDetailsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ok {
		n += 2
	}
	if len(m.Errors) > 0 {
		for _, s := range m.Errors {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionDetailByPromotionCodeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PromotionCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StockItemPriceAllChannelAndPromotion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ProductCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	if len(m.Promotions) > 0 {
		for _, e := range m.Promotions {
			l = e.Size()
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotionAllChannel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Code)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.Channel)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.Price != 0 {
		n += 9
	}
	if len(m.Rules) > 0 {
		for k, v := range m.Rules {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if len(m.Actions) > 0 {
		for k, v := range m.Actions {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovPromotion(uint64(len(k))) + 1 + len(v) + sovPromotion(uint64(len(v)))
			n += mapEntrySize + 1 + sovPromotion(uint64(mapEntrySize))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PromotedStockItemRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Dates) > 0 {
		for _, s := range m.Dates {
			l = len(s)
			n += 1 + l + sovPromotion(uint64(l))
		}
	}
	if len(m.StoreIds) > 0 {
		l = 0
		for _, e := range m.StoreIds {
			l += sovPromotion(uint64(e))
		}
		n += 1 + sovPromotion(uint64(l)) + l
	}
	l = len(m.CountryCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OK) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ok {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ExclusivePromoRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != 0 {
		n += 1 + sovPromotion(uint64(m.StoreId))
	}
	l = len(m.CountryCode)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	l = len(m.Channel)
	if l > 0 {
		n += 1 + l + sovPromotion(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Null) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovPromotion(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozPromotion(x uint64) (n int) {
	return sovPromotion(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *PromotionQuotaDetailInfoWithFilterMultiResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionQuotaDetailInfoWithFilterMultiResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionQuotaDetailInfoWithFilterMultiResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionQuotaDetailInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionQuotaDetailInfo = append(m.PromotionQuotaDetailInfo, &PromotionQuotaDetailInfoWithFilterResponse{})
			if err := m.PromotionQuotaDetailInfo[len(m.PromotionQuotaDetailInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionQuotaDetailInfoWithFilterResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionQuotaDetailInfoWithFilterResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionQuotaDetailInfoWithFilterResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Quota", wireType)
			}
			m.Quota = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Quota |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Active", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Active = bool(v != 0)
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionQuotaRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionQuotaRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionQuotaRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Quota", wireType)
			}
			m.Quota = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Quota |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Active", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Active = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionQuotaRequestMultiple) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionQuotaRequestMultiple: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionQuotaRequestMultiple: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImportPromotionQuotaRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImportPromotionQuotaRequest = append(m.ImportPromotionQuotaRequest, &ImportPromotionQuotaRequest{})
			if err := m.ImportPromotionQuotaRequest[len(m.ImportPromotionQuotaRequest)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionQuotaResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionQuotaResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionQuotaResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImportPromotionQuotaError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImportPromotionQuotaError = append(m.ImportPromotionQuotaError, &ImportPromotionQuotaError{})
			if err := m.ImportPromotionQuotaError[len(m.ImportPromotionQuotaError)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionQuotaError) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionQuotaError: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionQuotaError: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Errors", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Errors = append(m.Errors, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rules == nil {
				m.Rules = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Rules[mapkey] = mapvalue
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Actions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Actions == nil {
				m.Actions = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Actions[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionDetailRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionDetailRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionDetailRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rules == nil {
				m.Rules = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Rules[mapkey] = mapvalue
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Actions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Actions == nil {
				m.Actions = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Actions[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionChannelRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionChannelRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionChannelRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionChannelResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionChannelResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionChannelResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionChannelInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.PromotionChannelInfo == nil {
				m.PromotionChannelInfo = make(map[string]int64)
			}
			var mapkey string
			var mapvalue int64
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapvalue |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.PromotionChannelInfo[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StockItemPromotionMultiRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StockItemPromotionMultiRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StockItemPromotionMultiRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCodes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCodes = append(m.ProductCodes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StoreIds = append(m.StoreIds, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CountryCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CountryCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StockItemPromotionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StockItemPromotionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StockItemPromotionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rules == nil {
				m.Rules = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Rules[mapkey] = mapvalue
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Actions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Actions == nil {
				m.Actions = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Actions[mapkey] = mapvalue
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeletedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeletedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StockItemPromotionMultiResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StockItemPromotionMultiResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StockItemPromotionMultiResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StockItemPromotions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StockItemPromotions = append(m.StockItemPromotions, &StockItemPromotionResponse{})
			if err := m.StockItemPromotions[len(m.StockItemPromotions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProductCodes) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ProductCodes: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ProductCodes: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCodes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCodes = append(m.ProductCodes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionDetails", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionDetails = append(m.PromotionDetails, &ImportPromotionDetail{})
			if err := m.PromotionDetails[len(m.PromotionDetails)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionMultiRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionMultiRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionMultiRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImportPromotionRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImportPromotionRequest = append(m.ImportPromotionRequest, &ImportPromotionRequest{})
			if err := m.ImportPromotionRequest[len(m.ImportPromotionRequest)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImportPromotionError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImportPromotionError = append(m.ImportPromotionError, &ImportPromotionError{})
			if err := m.ImportPromotionError[len(m.ImportPromotionError)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportPromotionError) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportPromotionError: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportPromotionError: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Errors", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Errors = append(m.Errors, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionSource) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionSource: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionSource: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionSourceRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionSourceRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionSourceRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionSourceResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionSourceResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionSourceResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionSource", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionSource = append(m.PromotionSource, &PromotionSource{})
			if err := m.PromotionSource[len(m.PromotionSource)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionDetailInfoWithFilterRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionDetailInfoWithFilterRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionDetailInfoWithFilterRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPromotion
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPromotion
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionDetailInfoWithFilterResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionDetailInfoWithFilterResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionDetailInfoWithFilterResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rules == nil {
				m.Rules = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Rules[mapkey] = mapvalue
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Actions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Actions == nil {
				m.Actions = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Actions[mapkey] = mapvalue
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Source = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionsDeletedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionsDeletedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionDetailsDeletedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionDetailsDeletedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionDetailInfoWithFilterMultiResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionDetailInfoWithFilterMultiResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionDetailInfoWithFilterMultiResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionDetailInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionDetailInfo = append(m.PromotionDetailInfo, &PromotionDetailInfoWithFilterResponse{})
			if err := m.PromotionDetailInfo[len(m.PromotionDetailInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionCodes) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionCodes: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionCodes: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCodes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCodes = append(m.PromotionCodes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatesAndStoreIDs) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DatesAndStoreIDs: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DatesAndStoreIDs: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPromotion
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPromotion
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dates", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dates = append(m.Dates, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreIDDateChannel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreIDDateChannel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreIDDateChannel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Date = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeletePromotionDetailsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeletePromotionDetailsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeletePromotionDetailsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPromotion
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPromotion
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCodes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCodes = append(m.PromotionCodes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channels", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channels = append(m.Channels, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeletePromotionDetailsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeletePromotionDetailsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeletePromotionDetailsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ok", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Ok = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Errors", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Errors = append(m.Errors, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionDetailByPromotionCodeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionDetailByPromotionCodeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionDetailByPromotionCodeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PromotionCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PromotionCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StockItemPriceAllChannelAndPromotion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StockItemPriceAllChannelAndPromotion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StockItemPriceAllChannelAndPromotion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Promotions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Promotions = append(m.Promotions, &PromotionAllChannel{})
			if err := m.Promotions[len(m.Promotions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotionAllChannel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotionAllChannel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotionAllChannel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Code = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Price = float64(math.Float64frombits(v))
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rules == nil {
				m.Rules = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Rules[mapkey] = mapvalue
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Actions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Actions == nil {
				m.Actions = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthPromotion
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipPromotion(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthPromotion
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Actions[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PromotedStockItemRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PromotedStockItemRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PromotedStockItemRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dates", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dates = append(m.Dates, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPromotion
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPromotion
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthPromotion
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPromotion
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CountryCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CountryCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OK) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OK: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OK: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ok", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Ok = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExclusivePromoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExclusivePromoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExclusivePromoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CountryCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CountryCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthPromotion
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthPromotion
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Null) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Null: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Null: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPromotion(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthPromotion
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipPromotion(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowPromotion
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPromotion
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthPromotion
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupPromotion
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthPromotion
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthPromotion        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowPromotion          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupPromotion = fmt.Errorf("proto: unexpected end of group")
)
