syntax = "proto3";
package aloha.promo;

option go_package = "rpc";


service Api {
    // Promotion API
    rpc GetAllStockItemPromotion(StockItemPromotionMultiRequest) returns (StockItemPromotionMultiResponse) {}

    rpc GetAllStockItemPromotionChannel(StockItemPromotionMultiRequest) returns (StockItemPromotionMultiResponse) {}

    rpc GetPromotionDetailInfoWithFilter(PromotionDetailInfoWithFilterRequest) returns (PromotionDetailInfoWithFilterMultiResponse) {}

    rpc GetPromotedProductCodes(StoreIDDateChannel) returns (ProductCodes) {}

    rpc FindAllStartingPromotions(DatesAndStoreIDs) returns (StockItemPromotionMultiResponse) {}

    rpc StreamAllStartingPromotions(DatesAndStoreIDs) returns (stream StockItemPromotionResponse) {}

    rpc FindAllEndingPromotions(DatesAndStoreIDs) returns (StockItemPromotionMultiResponse) {}

    rpc StreamAllEndingPromotions(DatesAndStoreIDs) returns (stream StockItemPromotionResponse) {}

    rpc ImportPromotion(ImportPromotionMultiRequest) returns (ImportPromotionResponse) {}

    rpc StreamImportPromotion(stream ImportPromotionDetailRequest) returns (ImportPromotionResponse) {}
    
    rpc DeletePromotions(PromotionCodes) returns (OK) {}

    rpc GetPromotionDetailByPromotionCode(PromotionDetailByPromotionCodeRequest) returns (stream StockItemPromotionResponse) {}

    // Promotion Channel API
    rpc GetPromotionChannel(PromotionChannelRequest) returns (PromotionChannelResponse) {}

    // Promotion Source API
    rpc GetPromotionSource(PromotionSourceRequest) returns (PromotionSourceResponse) {}

    rpc DeletePromotionBasedOnSKU(stream DeletePromotionDetailsRequest) returns (DeletePromotionDetailsResponse) {}

    // Promotion reindex boosting
    rpc StreamActivePromotedStockItems(PromotedStockItemRequest) returns (stream StockItemPriceAllChannelAndPromotion) {}

    rpc StreamInactivePromotedStockItems(PromotedStockItemRequest) returns (stream StockItemPriceAllChannelAndPromotion) {}

    rpc Ping(Null) returns (OK) {}

    // Promotion Quota
    rpc ImportPromotionQuota(ImportPromotionQuotaRequestMultiple) returns (ImportPromotionQuotaResponse){}
    rpc StreamImportPromotionQuota(stream ImportPromotionQuotaRequest) returns (ImportPromotionQuotaResponse){}
    rpc GetPromotionQuotaDetailInfoWithFilter (ImportPromotionQuotaRequest) returns (PromotionQuotaDetailInfoWithFilterMultiResponse){}

    // Exclusive Promo API
    rpc GetExclusivePromotion (ExclusivePromoRequest) returns (ProductCodes){}
}

message PromotionQuotaDetailInfoWithFilterMultiResponse {
    repeated PromotionQuotaDetailInfoWithFilterResponse promotion_quota_detail_info = 1;
    int64 count = 2;
}

message PromotionQuotaDetailInfoWithFilterResponse {
    string promotion_code = 1;
    string product_code = 2;
    int32 quota = 3;
    bool active = 4;
    string created_at = 5;
    string updated_at = 6;
}

message ImportPromotionQuotaRequest {
    string promotion_code = 1;
    string product_code = 2;
    int32 quota = 3;
    bool active = 4;
}

message ImportPromotionQuotaRequestMultiple {
    repeated ImportPromotionQuotaRequest ImportPromotionQuotaRequest = 1;
}

message ImportPromotionQuotaResponse {
    repeated ImportPromotionQuotaError importPromotionQuotaError = 1;
}

message ImportPromotionQuotaError {
    string promotion_code = 1;
    string product_code = 2;
    repeated string errors = 3;   
}

message ImportPromotionDetail {
    string product_code = 1;
    int64 store_id = 2;
    int64 channel_id = 3;
    string type = 4;
    map<string, string> rules = 5;
    map<string, string> actions = 6;
}

message ImportPromotionDetailRequest {
    string promotion_code = 1;
    int64 source_id = 2;
    string start_date = 3;
    string last_date = 4;
    string product_code = 5;
    int64 store_id = 6;
    int64 channel_id = 7;
    string type = 8;
    map<string, string> rules = 9;
    map<string, string> actions = 10;
}

message PromotionChannelRequest {
}

message PromotionChannelResponse {
    map<string, int64> PromotionChannelInfo = 1;
}

message StockItemPromotionMultiRequest {
    repeated string product_codes = 1;
    repeated string store_ids = 2;
    string channel = 3;
    string country_code = 4;
}

message StockItemPromotionResponse {
    string promotion_code = 1;
    string start_date = 2;
    string last_date = 3;
    string product_code = 4;
    int64 store_id = 5;
    string type = 6;
    string channel = 7;
    map<string, string> rules = 8;
    map<string, string> actions = 9;
    string deleted_at = 10;
    int64 source_id = 11;
}

message StockItemPromotionMultiResponse {
    repeated StockItemPromotionResponse stock_item_promotions = 1;
}

message ProductCodes {
    repeated string product_codes = 1;
}

message ImportPromotionRequest {
    string promotion_code = 1;
    int64 source_id = 2;
    repeated ImportPromotionDetail promotion_details = 3;
    string start_date = 4;
    string last_date = 5;
    string created_at = 6;
    string updated_at = 7;
}

message ImportPromotionMultiRequest {
    repeated ImportPromotionRequest import_promotion_request = 1;
}

message ImportPromotionResponse {
    repeated ImportPromotionError importPromotionError = 1;
}

message ImportPromotionError {
    string promotion_code = 1;
    string product_code = 2;
    int64 store_id = 3;
    int64 channel_id = 4;
    repeated string errors = 5;
}

message PromotionSource {
    int64 id = 1;
    string name = 2;
}

message PromotionSourceRequest {}

message PromotionSourceResponse {
    repeated PromotionSource promotion_source = 1;
}

message PromotionDetailInfoWithFilterRequest {
    repeated int64 store_ids = 1;
    string product_code = 2;
    string promotion_code = 3;
    string start_date = 4;
    string last_date = 5;
    int64 source_id = 6;
    int64 size = 7;
    int64 from = 8;
}

message PromotionDetailInfoWithFilterResponse {
    string promotion_code = 1;
    string product_code = 2;
    int64 store_id = 3;
    map<string, string> rules = 4;
    map<string, string> actions = 5;
    string channel = 6;
    string source = 7;
    string start_date = 8;
    string last_date = 9;
    string created_at = 10;
    string updated_at = 11;
    string status = 12;
    string promotions_deleted_at = 13;
    string promotion_details_deleted_at = 14;
}

message PromotionDetailInfoWithFilterMultiResponse {
    repeated PromotionDetailInfoWithFilterResponse promotion_detail_info = 1;
    int64 count = 2;
}

message PromotionCodes {
    repeated string promotion_codes = 1;
}

message DatesAndStoreIDs {
    repeated int64 store_ids = 1;
    repeated string dates = 2;
}

message StoreIDDateChannel {
    int64 store_id = 1;
    string date = 2;
    string channel = 3;
}

message DeletePromotionDetailsRequest {
    string product_code = 1;
    repeated int64 store_ids = 2;
    repeated string promotion_codes = 3;
    repeated string channels = 4;
}

message DeletePromotionDetailsResponse {
    bool ok = 1;
    repeated string errors = 2;
}

message PromotionDetailByPromotionCodeRequest {
    string promotion_code = 1;
}

message StockItemPriceAllChannelAndPromotion {
  string product_code = 1;
	int64 store_id = 2;
	repeated PromotionAllChannel promotions = 3;
}

message PromotionAllChannel {
  string code = 1;
  string channel = 2;
  double price = 3;
  map<string, string> rules = 4;
  map<string, string> actions = 5;
}

message PromotedStockItemRequest {
  repeated string dates = 1;
  repeated int64 store_ids = 2;
  string country_code = 3;
}

message OK {
    bool ok = 1;
}

message ExclusivePromoRequest{
    int64 store_id = 1;
    string country_code = 2;
    string channel = 3;
}

message Null {}
