package kafka

import (
	"context"

	"github.com/Shopify/sarama"
)

// MessageHandler :nodoc:
type MessageHandler interface {
	Handler(context.Context, *sarama.ConsumerMessage)
}

// Consumer implement sarama.ConsumerGroupHandler
type Consumer struct {
	Name  string
	Ready chan bool
	MessageHandler
}

// NewConsumer :nodoc:
func NewConsumer(name string, MessageHandler MessageHandler) *Consumer {
	return &Consumer{
		Name:           name,
		Ready:          make(chan bool),
		MessageHandler: MessageHandler,
	}
}

// Setup run before ConsumeClaim
func (c *Consumer) Setup(session sarama.ConsumerGroupSession) error {
	close(c.Ready)
	return nil
}

// Cleanup run after ConsumeClaim
func (c *Consumer) Cleanup(session sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim claim consumer message
func (c *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		c.MessageHandler.Handler(session.Context(), message)
		session.MarkMessage(message, "")
	}
	return nil
}
