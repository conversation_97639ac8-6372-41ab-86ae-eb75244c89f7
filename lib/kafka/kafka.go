package kafka

import (
	"errors"
	"strings"

	"github.com/Shopify/sarama"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/lib/scram"
)

// Config :nodoc:
type Config struct {
	UserName  string
	Password  string
	Brokers   string
	Mechanism string
}

// NewConfig :nodoc:
func NewConfig(c Config) ([]string, *sarama.Config, error) {
	if (len(c.Password) == 0 && len(c.UserName) != 0) || (len(c.Password) != 0 && len(c.UserName) == 0) {
		err := errors.New("UserName/Password is missing")
		log.For("Kafka", "NewConfig").Error(err)
		return nil, nil, err
	}

	if len(c.Brokers) == 0 {
		err := errors.New("At least one broker is required")
		log.For("Kafka", "NewConfig").Error(err)
		return nil, nil, err
	}

	conf := sarama.NewConfig()
	conf.Producer.RequiredAcks = sarama.WaitForAll
	conf.Producer.Return.Successes = true
	conf.Metadata.Full = true
	conf.ClientID = "price_etl"
	conf.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategySticky
	conf.Version = sarama.V1_0_0_0

	if len(c.UserName) != 0 && len(c.Password) != 0 {
		conf.Net.TLS.Enable = true
		conf.Net.SASL.Enable = true
		conf.Net.SASL.User = c.UserName
		conf.Net.SASL.Password = c.Password
		conf.Net.SASL.Handshake = true

		switch mechanism := strings.ToUpper(c.Mechanism); mechanism {
		case "PLAIN":
			conf.Net.SASL.Mechanism = sarama.SASLMechanism(sarama.SASLTypePlaintext)
		case "SCRAM":
			conf.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient { return scram.NewXDGSCRAMClient() }
			conf.Net.SASL.Mechanism = sarama.SASLMechanism(sarama.SASLTypeSCRAMSHA256)
		default:
			err := errors.New("Invalid SASL Mechanism")
			log.For("Kafka", "NewConfig").Error(err)
			return nil, nil, err
		}
	}

	if err := conf.Validate(); err != nil {
		log.For("Kafka", "NewConfig").Error(err)
		return nil, nil, err
	}

	return strings.Split(c.Brokers, ","), conf, nil
}

// NewClient :nodoc:
func NewClient(c Config) (client sarama.Client, err error) {
	brokers, conf, err := NewConfig(c)
	if err != nil {
		log.For("Kafka", "NewClient").Error(err)
		return
	}

	client, err = sarama.NewClient(brokers, conf)
	if err != nil {
		log.For("Kafka", "NewClient").Error(err)
		return
	}

	return
}
