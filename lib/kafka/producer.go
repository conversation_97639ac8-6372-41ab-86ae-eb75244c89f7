package kafka

import (
	"github.com/Shopify/sarama"
	"happyfresh.io/lib/log"
)

// Producer :nodoc:
type Producer struct {
	Topic    string
	Producer sarama.SyncProducer
}

// NewProducer :nodoc:
func NewProducer(client sarama.Client, topic string) (*Producer, error) {
	syncProducer, err := sarama.NewSyncProducerFromClient(client)
	if err != nil {
		log.For("Kafka", "NewProducer").Errorf("Failed to create producer: ", err)
		return nil, err
	}

	return &Producer{
		Producer: syncProducer,
		Topic:    topic,
	}, nil
}

// SendMessage :nodoc:
func (p *Producer) SendMessage(msg []byte) (err error) {
	message := &sarama.ProducerMessage{Topic: p.Topic, Value: sarama.ByteEncoder(msg)}
	_, _, err = p.Producer.SendMessage(message)
	if err != nil {
		log.For("Kafka", "Publish").Error(err)
		return
	}

	return
}

// Close :nodoc:
func (p *Producer) Close() (err error) {
	if err = p.Producer.Close(); err != nil {
		log.For("Kafka", "Close Publisher").Error(err)
		return
	}
	return
}
