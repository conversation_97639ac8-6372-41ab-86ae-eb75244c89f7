package kafka

import (
	"github.com/Shopify/sarama"
	"happyfresh.io/lib/log"
)

var kafkaClient sarama.Client

func Init(username, password, brokers, mechanism string) error {
	var err error

	kafkaCfg := Config{
		UserName:  username,
		Password:  password,
		Brokers:   brokers,
		Mechanism: mechanism,
	}

	kafkaClient, err = NewClient(kafkaCfg)
	if err != nil {
		log.For("kafka", "client").Error(err)
		return err
	}

	return nil
}

func Produce(topic string, message []byte) error {
	producer, err := NewProducer(kafkaClient, topic)
	if err != nil {
		log.For("kafka", "produce").Error(err)
		return err
	}
	defer producer.Close()

	if err := producer.SendMessage(message); err != nil {
		log.For("kafka", "produce").Error(err)
		return err
	}

	return nil
}
