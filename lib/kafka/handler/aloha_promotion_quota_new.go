package handler

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/Shopify/sarama"
	"happyfresh.io/lib/log"
	"happyfresh.io/product-promotion/config"
	"happyfresh.io/product-promotion/db"
)

const (
	THRESHOLD_QUOTA = 1
)

// QuotaMessageHandler implement kafka.MessageHandler
type QuotaMessageHandler struct {
	Wg    *sync.WaitGroup
	Notif chan QuotaNotification
}

// NewQuotaMessageHandler :nodoc:
func NewQuotaMessageHandler(wg *sync.WaitGroup, notif chan QuotaNotification) *QuotaMessageHandler {
	return &QuotaMessageHandler{Wg: wg, Notif: notif}
}

// InternalPriceMessageHandler :nodoc:
func (a *QuotaMessageHandler) Handler(ctx context.Context, message *sarama.ConsumerMessage) {
	a.Wg.Add(1)
	defer a.Wg.Done()
	switch message.Topic {
	case config.KafkaTopics():
		var spreeMessage SpreeMessage
		err := json.Unmarshal(message.Value, &spreeMessage)
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("json UnMarshal Error %v", err)
			return
		}

		switch spreeMessage.Type {
		case "order_completed":
			if spreeMessage.Data.Order.CompletedAt == nil {
				return
			}

			multiple := 1
			updatePromoQuota(&spreeMessage, multiple, a.Notif)
		case "order_cancelled":
			if spreeMessage.Data.Order.CanceledAt == nil {
				return
			}

			multiple := -1
			updatePromoQuota(&spreeMessage, multiple, a.Notif)
		case "order_edited":
			editPromoQuota(&spreeMessage, a.Notif)
		}

	default:
		log.For("Handler", "ConsumerGroupHandler").Errorf("Unknown topic %s", message.Topic)
	}
}

func editPromoQuota(spreeMessage *SpreeMessage, notif chan QuotaNotification) {
	var country string
	country, err := spreeMessage.Country()
	if err != nil {
		return
	}

	var promotionBaseQuotas []db.PromotionQuotaLog
	noOrder := spreeMessage.Data.Order.Number
	for _, data := range spreeMessage.Data.OrderItems {
		var promotionBaseQuota db.PromotionQuotaLog
		promotionBaseQuota.Quota = data.PromoQuantity
		err := promotionBaseQuota.QuantityOrder.Scan(data.PromoQuantity)
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("Promo Quantity	 Error %v", err)
			continue
		}

		promotionBaseQuota.PromotionCode = data.PromotionCode
		promotionBaseQuota.ProductCode = data.ProductCode
		err = promotionBaseQuota.NoOrder.Scan(noOrder)
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("No Order Error %v", err)
			continue
		}

		promotionBaseQuotas = append(promotionBaseQuotas, promotionBaseQuota)
	}

	var productCodes []string
	for _, data := range promotionBaseQuotas {
		productCodes = append(productCodes, data.ProductCode)
		promotionData, err := db.NewPromotionQuotaLog(nil).FindByProductCodeNNoOrder(noOrder, data.ProductCode)
		if err == sql.ErrNoRows {
			tx, err := db.GetDB().BeginTx(context.Background(), &sql.TxOptions{})
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("Open Transaction Error %v", err)
				continue
			}

			req := db.NewInsertPromotionQuotaLogRequest(0, data.ProductCode, 0, data.Active, noOrder, int(data.QuantityOrder.Int32), data.PromotionCode)
			err = db.NewPromotionQuotaLog(tx).InsertByPromoCodeWithTx(req)
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("InsertByPromoCodeWithTx Error %v", err)
				continue
			}

			req.PromotionID, err = db.NewPromotionTable().GetIdByPromotionCode(req.PromoCode)
			if err != nil {
				err = tx.Rollback()
				if err != nil {
					log.For("Handler", "ConsumerGroupHandler").
						Errorf("rollback Transaction Error %v", err)
					continue
				}

				log.For("Handler", "ConsumerGroupHandler").
					Errorf("GetIdByPromotionCode Error %v", err)
				continue
			}

			err = db.NewPromotionQuota(tx).UpdateQuotaByPromoIDNProdCodeWithTx(req.PromotionID,
				req.ProductCode, int(req.QuantityOrder))
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("GetIdByPromotionCode Error %v", err)
				continue
			}

			err = tx.Commit()
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("Commit Transaction Error %v", err)
				continue
			}

			quota, err := db.NewPromotionQuota(nil).GetQuotaByPromotionIDNProdCode(req.PromotionID, data.ProductCode)
			if err != nil {
				continue
			}

			notif <- QuotaNotification{
				data.PromotionCode,
				country,
				req.ProductCode,
				quota,
			}

		} else if err != nil {
			continue
		}

		activePromotion, err := db.NewPromotionQuota(nil).GetActiveByDatePromotionIDProductCode(promotionData.PromotionId, data.ProductCode)
		if err == sql.ErrNoRows {
			continue
		} else if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("get Active Promo Error %v", err)
			continue
		}

		diffrentQuantity := data.QuantityOrder.Int32 - promotionData.QuantityOrder.Int32
		if (diffrentQuantity > 0 && activePromotion) || diffrentQuantity < 0 {
			tx, err := db.GetDB().BeginTx(context.Background(), &sql.TxOptions{})
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("Open Transaction Error %v", err)
				continue
			}

			req := db.NewInsertPromotionQuotaLogRequest(promotionData.PromotionId, promotionData.ProductCode,
				promotionData.Quota, promotionData.Active, noOrder, int(diffrentQuantity), "")

			err = db.NewPromotionQuotaLog(tx).InsertWithTx(req)
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("GetIdByPromotionCode Error %v", err)
				continue
			}

			err = db.NewPromotionQuota(tx).UpdateQuotaByPromoIDNProdCodeWithTx(req.PromotionID,
				req.ProductCode, req.QuantityOrder)
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("GetIdByPromotionCode Error %v", err)
				continue
			}

			err = tx.Commit()
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("Commit Transaction Error %v", err)
				continue
			}

			quota, err := db.NewPromotionQuota(nil).GetQuotaByPromotionIDNProdCode(promotionData.PromotionId, data.ProductCode)
			if err != nil {
				continue
			}

			notif <- QuotaNotification{
				data.PromotionCode,
				country,
				promotionData.ProductCode,
				quota,
			}
		}
	}

	c := make([]string, len(productCodes))
	for i, productCode := range productCodes {
		c[i] = fmt.Sprintf("'%s'", productCode)
	}

	pCodes := strings.Join(c, ",")
	dataRemoveFromOrder, err := db.NewPromotionQuotaLog(nil).SumQtyOrderMoreThanZeroNotInProductCode(pCodes, noOrder)
	if err != nil {
		log.For("Handler", "ConsumerGroupHandler").
			Errorf("Get Quota Log More Than Zero Error %v", err)
		return
	}

	for _, data := range dataRemoveFromOrder {
		tx, err := db.GetDB().BeginTx(context.Background(), &sql.TxOptions{})
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("Open Transaction Error %v", err)
			continue
		}

		req := db.NewInsertPromotionQuotaLogRequest(data.PromotionId, data.ProductCode,
			data.Quota, data.Active, noOrder, int(data.QuantityOrder.Int32*-1), "")

		err = db.NewPromotionQuotaLog(tx).InsertWithTx(req)
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("InsertWithTx Error %v", err)
			continue
		}

		err = db.NewPromotionQuota(tx).UpdateQuotaByPromoIDNProdCodeWithTx(data.PromotionId,
			data.ProductCode, int(data.QuantityOrder.Int32*-1))
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("UpdateQuotaByPromoIDNProdCodeWithTx Error %v", err)
			continue
		}

		err = tx.Commit()
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("Commit Transaction Error %v", err)
			continue
		}
	}
}

func updatePromoQuota(spreeMessage *SpreeMessage, multiple int, notif chan QuotaNotification) {
	var country string
	country, err := spreeMessage.Country()
	if err != nil {
		return
	}

	var promotionCode []string
	var promotionBaseQuotas []db.PromotionQuotaLog
	noOrder := spreeMessage.Data.Order.Number
	for _, data := range spreeMessage.Data.OrderItems {
		var promotionBaseQuota db.PromotionQuotaLog
		if strings.TrimSpace(data.PromotionCode) != "" && data.PromoQuantity > 0 {
			promotionCode = append(promotionCode, data.PromotionCode)
			err := promotionBaseQuota.QuantityOrder.Scan(data.PromoQuantity * multiple)
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("Promo Quantity	 Error %v", err)
				continue
			}

			promotionBaseQuota.ProductCode = data.ProductCode
			promotionBaseQuota.PromotionCode = data.PromotionCode
			err = promotionBaseQuota.NoOrder.Scan(noOrder)
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("No Order Error %v", err)
				continue
			}

			promotionBaseQuotas = append(promotionBaseQuotas, promotionBaseQuota)
		}
	}

	if len(promotionCode) < 1 {
		return
	}

	datapromo, err := db.NewPromotionTable().FindIdAndPromoCodeByPromoCode(promotionCode)
	if err != nil {
		log.For("Handler", "ConsumerGroupHandler").
			Errorf("Find Promotion Code %v", err)
		return
	}

	for _, promotion := range promotionBaseQuotas {
		_, found := datapromo[promotion.PromotionCode]
		if !found {
			continue
		}

		tx, err := db.GetDB().BeginTx(context.Background(), &sql.TxOptions{})
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("Open Transaction Error %v", err)
			continue
		}

		req := db.NewInsertPromotionQuotaLogRequest(datapromo[promotion.PromotionCode], promotion.ProductCode,
			promotion.Quota, promotion.Active, noOrder, int(promotion.QuantityOrder.Int32), "")
		err = db.NewPromotionQuotaLog(tx).InsertWithTx(req)
		if err != nil {
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("rollback Transaction Error %v", err)
			}
			continue
		}

		err = db.NewPromotionQuota(tx).UpdateQuotaByPromoIDNProdCodeWithTx(req.PromotionID,
			req.ProductCode, req.QuantityOrder)
		if err != nil {
			if err != nil {
				log.For("Handler", "ConsumerGroupHandler").
					Errorf("rollback Transaction Error %v", err)
			}
			continue
		}

		err = tx.Commit()
		if err != nil {
			log.For("Handler", "ConsumerGroupHandler").
				Errorf("Commit Transaction Error %v", err)
			continue
		}

		quota, err := db.NewPromotionQuota(nil).GetQuotaByPromotionIDNProdCode(datapromo[promotion.PromotionCode], promotion.ProductCode)
		if err != nil {
			continue
		}

		notif <- QuotaNotification{
			promotion.PromotionCode,
			country,
			promotion.ProductCode,
			quota,
		}
	}
}

type QuotaNotification struct {
	PromotionCode string `json:"promotion_code"`
	Country       string `json:"country"`
	ProductCode   string `json:"product_code"`
	Quota         int    `json:"quota"`
}

func SendNotifQuota(ctx context.Context, notif chan QuotaNotification, wg *sync.WaitGroup, terminate chan bool) {
	log.Info("notif worker initiated")
	type sendWebHook struct {
		Text string `json:"text"`
	}

	for {
		select {
		case data := <-notif:
			wg.Add(1)
			defer wg.Done()
			var url string
			if data.Country == "ID" {
				url = config.WebHookID()
			} else if data.Country == "TH" {
				url = config.WebHookTH()
			} else {
				url = config.WebHookMY()
			}

			if data.Quota < THRESHOLD_QUOTA {
				dataNotif := sendWebHook{
					Text: "Promotion " + data.PromotionCode + " remaining quota is " + strconv.Itoa(data.Quota) + " with Product Code : " + data.ProductCode,
				}

				payload, err := json.Marshal(dataNotif)
				if err != nil {
					continue
				}

				client := &http.Client{}
				req, err := http.NewRequest("POST", url, bytes.NewBuffer(payload))
				if err != nil {
					continue
				}

				req.Header.Add("Content-Type", "application/json; charset=UTF-8")
				res, err := client.Do(req)
				if err != nil {
					continue
				}

				defer res.Body.Close()
			}

		case <-terminate:
			log.Info("Close notif worker")
			return
		}
	}
}

// SpreeMessage :nodoc:
type SpreeMessage struct {
	Type string `json:"type"`
	Data struct {
		Order struct {
			ID                int         `json:"id"`
			Number            string      `json:"number"`
			State             string      `json:"state"`
			UserID            int         `json:"user_id"`
			CompletedAt       *time.Time  `json:"completed_at"`
			Email             string      `json:"email"`
			CancelReason      interface{} `json:"cancel_reason"`
			CanceledAt        *time.Time  `json:"canceled_at"`
			DeliveryStartTime *time.Time  `json:"delivery_start_time"`
			DeliveryEndTime   *time.Time  `json:"delivery_end_time"`
		} `json:"order"`
		OrderItems []struct {
			ProductCode   string `json:"product_code"`
			Sku           string `json:"sku"`
			Quantity      int    `json:"quantity"`
			StoreID       int    `json:"store_id"`
			ShopperNotes  string `json:"shopper_notes"`
			PromoQuantity int    `json:"promo_quantity"`
			PromotionCode string `json:"promotion_code"`
		} `json:"order_items"`
	} `json:"data"`
}

// Country :nodoc:
func (s SpreeMessage) Country() (string, error) {
	if len(s.Data.OrderItems) > 0 {
		if strings.Contains(s.Data.OrderItems[0].Sku, "-ID") {
			return "ID", nil
		} else if strings.Contains(s.Data.OrderItems[0].Sku, "-TH") {
			return "TH", nil
		}

		return "MY", nil
	}

	return "", errors.New("order item not found")
}
