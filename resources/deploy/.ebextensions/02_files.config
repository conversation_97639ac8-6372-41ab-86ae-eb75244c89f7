files:
  "/opt/elasticbeanstalk/hooks/appdeploy/enact/02_chmod.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/bin/bash
      echo "Begin chmod"
      chmod a+x /var/app/current/psrv
      echo "End chmod"
  "/opt/elasticbeanstalk/hooks/appdeploy/enact/03_migrate_up.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/bin/bash
      echo "Begin DB migration"
      eval $(/opt/elasticbeanstalk/bin/get-config --output YAML environment|perl -ne "/^\w/ or next; s/: /=/; print qq|\$_|" | awk '{print "export " $1}')
      /var/app/current/psrv migrate up
      echo "End DB migration"
