Resources:
  AWSEBAutoScalingGroup:
    Metadata:
      AWS::CloudFormation::Authentication:
        S3Auth:
          type: "s3"
          buckets: ["pnq-ap-southeast-1-448938462703"]
          roleName:
            "Fn::GetOptionSetting":
              Namespace: "aws:autoscaling:launchconfiguration"
              OptionName: "IamInstanceProfile"
              DefaultValue: "aws-elasticbeanstalk-ec2-role"
files:
  "/home/<USER>/Latest-PipelineBuild.zip":
      mode: "000755"
      owner: webapp
      group: webapp
      authentication: "S3Auth"
      source: https://s3-ap-southeast-1.amazonaws.com/elasticbeanstalk-ap-southeast-1-448938462703/CatalogService-Workflow-PRICESRV/Latest-PipelineBuild.zip
