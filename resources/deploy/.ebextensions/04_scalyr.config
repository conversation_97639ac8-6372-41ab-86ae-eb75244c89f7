files:
  "/tmp/scalyr.json":
    mode: "000755"
    owner: root
    group: root
    content: |
      {
        "api_key": "`{"Fn::GetOptionSetting": {"Namespace": "aws:elasticbeanstalk:application:environment", "OptionName": "PSRV_SCALYR_API_KEY", "DefaultValue": "12345678"}}`",
        "server_attributes": {
          "environment": "`{"Fn::GetOptionSetting": {"Namespace": "aws:elasticbeanstalk:application:environment", "OptionName": "PSRV_ENV", "DefaultValue": "staging"}}`"
        },
        "logs": [
          {
            "path": "/var/log/web-1.log",
            "attributes": {
              "service": "promotion",
              "parser": "json"
            }
          },
          {
            "path": "/var/log/web-1.error.log",
            "attributes": {
              "service": "promotion",
              "parser": "json"
            }
          }
        ],
        "monitors": [
        ]
      }

commands:
  01-wget:
    command: "wget -q https://www.scalyr.com/scalyr-repo/stable/latest/scalyr-repo-bootstrap-1.2.1-1.alt.noarch.rpm"
  02-removeBootstrap:
    command: "yum remove -y scalyr-repo scalyr-repo-bootstrap # Remove any previous repository definitions, if any."
  03-installBootstrap:
    command: "yum install -y --nogpgcheck scalyr-repo-bootstrap-1.2.1-1.alt.noarch.rpm"
  04-installScalyrRepo:
    command: "yum install -y scalyr-repo"
  05-installScalyrAgent2:
    command: "yum install -y scalyr-agent-2"
  06-agentFile:
    command: "cp /tmp/scalyr.json /etc/scalyr-agent-2/agent.json"
  07-start:
    command: "scalyr-agent-2 restart"