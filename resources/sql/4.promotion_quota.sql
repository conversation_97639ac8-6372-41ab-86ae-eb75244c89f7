-- name: insert-single-promotion-quota
INSERT INTO aloha_promotion_quota_new (promotion_id, product_code, quota, active,created_at,updated_at)
VALUES ($1 , $2, $3, $4,now(),now())
ON CONFLICT(promotion_id,product_code) DO 
UPDATE SET 
    quota  = $3,
    active = $4,
    updated_at = now()

-- name: get-promotion-quota-by-product-code-and-promotion-and-active
select aloha_promotions.promotion_code,aloha_promotion_quota_new.product_code,aloha_promotion_quota_new.quota,aloha_promotion_quota_new.active,aloha_promotion_quota_new.created_at,aloha_promotion_quota_new.updated_at from aloha_promotion_quota_new join aloha_promotions on aloha_promotion_quota_new.promotion_id = aloha_promotions.id WHERE aloha_promotions.promotion_code = $1 AND aloha_promotion_quota_new.product_code = $2 AND aloha_promotion_quota_new.active = $3;

-- name: get-promotion-quota-by-product-code-and-active
select aloha_promotions.promotion_code,aloha_promotion_quota_new.product_code,aloha_promotion_quota_new.quota,aloha_promotion_quota_new.active,aloha_promotion_quota_new.created_at,aloha_promotion_quota_new.updated_at from aloha_promotion_quota_new join aloha_promotions on aloha_promotion_quota_new.promotion_id = aloha_promotions.id WHERE aloha_promotion_quota_new.product_code = $1 AND aloha_promotion_quota_new.active = $2;

-- name: get-promotion-quota-by-promo-code-and-active
select aloha_promotions.promotion_code,aloha_promotion_quota_new.product_code,aloha_promotion_quota_new.quota,aloha_promotion_quota_new.active ,aloha_promotion_quota_new.created_at,aloha_promotion_quota_new.updated_at from aloha_promotion_quota_new join aloha_promotions on aloha_promotion_quota_new.promotion_id = aloha_promotions.id WHERE aloha_promotions.promotion_code = $1 AND aloha_promotion_quota_new.active = $2;

-- name: get-promotion-quota-by-active
select aloha_promotions.promotion_code,aloha_promotion_quota_new.product_code,aloha_promotion_quota_new.quota,aloha_promotion_quota_new.active ,aloha_promotion_quota_new.created_at,aloha_promotion_quota_new.updated_at from aloha_promotion_quota_new join aloha_promotions on aloha_promotion_quota_new.promotion_id = aloha_promotions.id WHERE aloha_promotion_quota_new.active = $1;

-- name: update-single-promotion-quota
UPDATE aloha_promotion_quota_new SET promotion_id = $1 , product_code = $2, quota  = quota - $3, active = 
    CASE WHEN quota - $3 > 0  THEN TRUE
    WHEN quota - $3 <= 0  THEN FALSE END 
    ,updated_at = now() WHERE promotion_id = $1 AND product_code = $2

-- name: update-single-promotion-quota-cancel
UPDATE aloha_promotion_quota_new SET promotion_id = $1 , product_code = $2, quota  = quota + $3, active = 
    CASE WHEN quota + $3 > 0  THEN TRUE
    WHEN quota + $3 <= 0  THEN FALSE END 
    ,updated_at = now() WHERE promotion_id = $1 AND product_code = $2

-- name : update-promotion-quota
UPDATE aloha_promotion_quota_new SET quota  = quota - $1,
active = CASE WHEN quota - $1 > 0 THEN TRUE
WHEN quota - $1 <= 0 THEN FALSE END,
updated_at = now()
WHERE aloha_promotion_quota_new.promotion_id = $2 
AND aloha_promotion_quota_new.product_code = $3;

-- name : get-active-promotion-by-promotion-id
select aloha_promotion_quota_new.active from aloha_promotions inner join aloha_promotion_quota_new on aloha_promotions.id = aloha_promotion_quota_new.promotion_id where aloha_promotions.new_start_date <= $1 and aloha_promotions.new_last_date >= $1 and aloha_promotions.id = $2 and aloha_promotion_quota_new.product_code = $3;

-- name : get-quota-promotion-by-promotion-id
select aloha_promotion_quota_new.quota from aloha_promotions inner join aloha_promotion_quota_new on aloha_promotions.id = aloha_promotion_quota_new.promotion_id where aloha_promotions.new_start_date <= $1 and aloha_promotions.new_last_date >= $1 and aloha_promotions.id = $2 and aloha_promotion_quota_new.product_code = $3;