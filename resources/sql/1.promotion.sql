-- name: create-promotion
INSERT INTO aloha_promotions (promotion_code, source_id, start_date ,new_start_date, last_date,new_last_date, created_at, updated_at)
VALUES ($1, $2, to_date($3, 'yyyy/MM/dd'),to_timestamp($3, 'yyyy/MM/dd HH24:MI:SS'), to_date($4,'yyyy/MM/dd'), to_timestamp($4, 'yyyy/MM/dd HH24:MI:SS'), now(), now())
ON CONFLICT(promotion_code) DO
UPDATE SET
    source_id = $2,
    start_date = to_date($3,'yyyy/MM/dd'),
    new_start_date = to_timestamp($3, 'yyyy/MM/dd HH24:MI:SS'),
    last_date = to_date($4,'yyyy/MM/dd'),
    new_last_date = to_timestamp($4, 'yyyy/MM/dd HH24:MI:SS'),
    updated_at = now(),
    deleted_at = NULL
RETURNING id;

-- name: create-promotion-detail
INSERT INTO aloha_promotion_details (promotion_id, product_code, store_id, channel_id, type, rules, actions)
VALUES ($1, $2, $3, $4, $5, $6, $7)
ON CONFLICT(promotion_id, product_code, store_id, channel_id) DO
UPDATE SET
    rules = $6,
    actions = $7,
    deleted_at = NULL
;

-- name: get-active-promos-by-product-code-store-id-channel-date
SELECT p.promotion_code, p.new_start_date, p.new_last_date, pd.product_code, pd.store_id, pd.type, pd.rules, pd.actions,p.source_id,apq.quota,apq.active
FROM aloha_promotion_details pd
INNER JOIN aloha_promotions p ON p.id = pd.promotion_id
INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id
LEFT JOIN aloha_promotion_quota_new apq ON apq.promotion_id = p.id AND apq.product_code = pd.product_code
WHERE p.deleted_at IS NULL
AND pd.deleted_at IS NULL
AND p.new_start_date <= $1
AND p.new_last_date >= $1
AND c.name = $2
AND pd.product_code IN (%s)
AND pd.store_id IN (%s)

-- name: get-active-promos-by-product-code-store-id-date
SELECT p.promotion_code, p.new_start_date, p.new_last_date, pd.product_code, pd.store_id, c.name, pd.type, pd.rules, pd.actions,p.source_id,apq.quota,apq.active
FROM aloha_promotion_details pd
INNER JOIN aloha_promotions p ON p.id = pd.promotion_id
INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id
LEFT JOIN aloha_promotion_quota_new apq ON apq.promotion_id = p.id AND apq.product_code = pd.product_code
WHERE p.deleted_at IS NULL
AND pd.deleted_at IS NULL
AND p.new_start_date <= $1
AND p.new_last_date >= $1
AND pd.product_code IN (%s)
AND pd.store_id IN (%s);

-- name: get-overlap-promo
SELECT promotion_code, to_char(p.new_start_date, 'yyyy/MM/dd HH24:MI:SS'), to_char(p.new_last_date, 'yyyy/MM/dd HH24:MI:SS')
FROM aloha_promotion_details pd
INNER JOIN aloha_promotions p ON p.id = promotion_id
WHERE promotion_code <> $1
AND p.deleted_at IS NULL
AND pd.deleted_at IS NULL
AND store_id = $2
AND product_code = $3
AND channel_id = $4
AND (
    (new_start_date <= to_timestamp($5, 'yyyy/MM/dd HH24:MI:SS') AND new_last_date >= to_timestamp($5, 'yyyy/MM/dd HH24:MI:SS'))
    OR
    (new_start_date <= to_timestamp($6, 'yyyy/MM/dd HH24:MI:SS') AND new_last_date >= to_timestamp($6, 'yyyy/MM/dd HH24:MI:SS'))
)

-- name: get-promotion-detail-with-filter
SELECT p.promotion_code,
pd.product_code,
pd.store_id,
pd.actions,
pd.rules,
c.name,
s.name,
to_char(p.new_start_date, 'yyyy/MM/dd HH24:MI:SS') AS start_date,
to_char(p.new_last_date, 'yyyy/MM/dd HH24:MI:SS') AS last_date,
to_char(p.created_at, 'yyyy/MM/dd HH24:MI:SS') AS created_at,
to_char(p.updated_at, 'yyyy/MM/dd HH24:MI:SS') AS updated_at,
p.deleted_at AS promotions_deleted_at,
pd.deleted_at AS promotion_details_deleted_at
FROM aloha_promotion_details pd
INNER JOIN aloha_promotions p ON pd.promotion_id = p.id
INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id
INNER JOIN aloha_promotion_sources s ON s.id = p.source_id
WHERE pd.store_id IN (%s) 
%s;

-- name: get-promotion-detail-with-filter-count
SELECT COUNT (pd.id)
FROM aloha_promotion_details pd
INNER JOIN aloha_promotions p ON pd.promotion_id = p.id
INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id
INNER JOIN aloha_promotion_sources s ON s.id = p.source_id
WHERE pd.store_id IN (%s) 
%s;

-- name: get-promoted-product-codes-by-date-store-id-channel
SELECT pd.product_code 
FROM aloha_promotion_details pd
INNER JOIN aloha_promotions p ON p.id = pd.promotion_id
INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id
LEFT JOIN aloha_promotion_quota_new apq ON apq.promotion_id = pd.promotion_id AND apq .product_code = pd.product_code 
WHERE p.deleted_at IS NULL
AND pd.deleted_at IS NULL
AND p.new_start_date <= $1
AND p.new_last_date >= $1
AND pd.store_id = $2
AND (apq.quota IS NULL OR apq.quota > 0)
AND (apq.active IS NULL OR apq.active = TRUE )
AND c.name = $3;

-- name: delete-promotion-by-codes
UPDATE aloha_promotions
SET deleted_at = now()
WHERE deleted_at IS NULL
AND promotion_code IN (%s)
RETURNING id;

-- name: delete-promotion-by-codes-custom-time
UPDATE aloha_promotions
SET deleted_at = $1
WHERE promotion_code IN (%s)
RETURNING id;

-- name: truncate-db
truncate table aloha_promotion_details, aloha_promotion_channels, aloha_promotion_quota, aloha_promotions, aloha_promotion_sources, schema_migrations restart identity;

-- name: delete-promotion-by-sku
UPDATE aloha_promotion_details
SET deleted_at = NOW()
WHERE deleted_at IS NULL
AND product_code = $1
%s;

-- name: get-promotion-detail-by-promotion-code
SELECT 
	ap.promotion_code,
	ap.new_start_date,
	ap.new_last_date,
    apd.product_code, 
    apd.store_id,
    apd.type,
    apc.name AS channel,
    apd.rules,
    apd.actions,
    ap.source_id,
    CASE
	    WHEN ap.deleted_at IS NULL THEN apd.deleted_at
	    ELSE ap.deleted_at
	END AS deleted_at
FROM aloha_promotions ap
INNER JOIN aloha_promotion_details apd ON ap.id=apd.promotion_id
INNER JOIN aloha_promotion_channels apc ON apd.channel_id=apc.id
AND ap.promotion_code = $1

-- name: get-id-and-promotion-code-by-promotion-code
SELECT id,promotion_code FROM aloha_promotions WHERE promotion_code IN (%s)

-- name : get-id-promotion-by-promotion-code
select id from aloha_promotions where aloha_promotions.promotion_code = $1;

-- name : get-exclusive-promo
SELECT pd.product_code 
FROM aloha_promotion_details pd
INNER JOIN aloha_promotions p ON p.id = pd.promotion_id
INNER JOIN aloha_promotion_channels c ON c.id = pd.channel_id
LEFT JOIN aloha_promotion_quota_new apq ON apq.promotion_id = pd.promotion_id AND apq .product_code = pd.product_code 
WHERE p.deleted_at IS NULL
AND pd.deleted_at IS NULL
AND p.new_start_date <= $1
AND p.new_last_date >= $1
AND pd.store_id = $2
AND p.source_id in ($3)
AND (apq.quota IS NULL OR apq.quota > 0)
AND (apq.active IS NULL OR apq.active = TRUE )
AND c.name = $4;