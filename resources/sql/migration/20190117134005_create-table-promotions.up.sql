CREATE TABLE IF NOT EXISTS aloha_promotions (
    id SERIAL PRIMARY KEY NOT NULL,
    source_id INTEGER NOT NULL REFERENCES aloha_promotion_sources (id),
    promotion_code VARCHAR(255) NOT NULL,
    start_date DATE NOT NULL,
    last_date DATE NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    deleted_at TIMESTAMP
);

CREATE UNIQUE INDEX aloha_promotions_promotion_code_unique_idx ON aloha_promotions (promotion_code);
