CREATE TABLE IF NOT EXISTS aloha_promotion_details (
    id SERIAL PRIMARY KEY NOT NULL,
    promotion_id INTEGER NOT NULL REFERENCES aloha_promotions (id),
    product_code VARCHAR(255),
    store_id INTEGER,
    channel_id INTEGER REFERENCES aloha_promotion_channels (id),
    type VARCHAR(255) NOT NULL,
    rules hstore,
    actions hstore
);

CREATE UNIQUE INDEX aloha_promotion_details_uniq_idx ON aloha_promotion_details (promotion_id, product_code, store_id, channel_id);
