DROP TRIGGER upsert_quota_logs ON aloha_promotion_quota_new_logs CASCADE;
ALTER TABLE aloha_promotion_quota_new_logs DROP no_order;
ALTER TABLE aloha_promotion_quota_new_logs DROP quantity_order;

CREATE OR REPLACE FUNCTION insertlogquota() RETURNS TRIGGER AS $$
   BEGIN
      INSERT INTO aloha_promotion_quota_new_logs(product_code,promotion_id,quota,active,created_at,updated_at) VALUES (new.product_code,new.promotion_id,new.quota,new.active, current_timestamp,current_timestamp);
      RETURN NEW;
   END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER upsert_quota_logs AFTER INSERT OR UPDATE ON aloha_promotion_quota_new
FOR EACH ROW EXECUTE PROCEDURE insertlogquota();

