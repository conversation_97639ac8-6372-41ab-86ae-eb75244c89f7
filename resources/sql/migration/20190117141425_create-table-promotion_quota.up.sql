CREATE TABLE IF NOT EXISTS aloha_promotion_quota (
    id SERIAL PRIMARY KEY NOT NULL,
    level_type VARCHAR(255) NOT NULL,
    level_id VARCHAR(255) NOT NULL,
    value INTEGER NOT NULL,
    used INTEGER NOT NULL DEFAULT 0
);

CREATE INDEX aloha_promotion_quota_level_type_idx ON aloha_promotion_quota (level_type);

CREATE INDEX aloha_promotion_quota_level_id_idx ON aloha_promotion_quota (level_id);

