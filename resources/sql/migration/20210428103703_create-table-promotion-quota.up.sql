CREATE TABLE aloha_promotion_quota_new (
    id SERIAL PRIMARY KEY,
    product_code character varying(255) NOT NULL,
    quota integer,
    active boolean,
    promotion_id integer NOT NULL REFERENCES aloha_promotions(id),
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);

CREATE INDEX aloha_promotion_quota_new_active_idx ON aloha_promotion_quota_new(active bool_ops);
CREATE UNIQUE INDEX aloha_promotion_quota_new_promotion_id_product_code_idx ON aloha_promotion_quota_new(promotion_id int4_ops,product_code text_ops);

CREATE TABLE aloha_promotion_quota_new_logs (
    id SERIAL PRIMARY KEY,
    product_code character varying(255) NOT NULL,
    quota integer,
    active boolean,
    promotion_id integer NOT NULL REFERENCES aloha_promotions(id),
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);

CREATE OR REPLACE FUNCTION insertlogquota() RETURNS TRIGGER AS $$
   BEGIN
      INSERT INTO aloha_promotion_quota_new_logs(product_code,promotion_id,quota,active,created_at,updated_at) VALUES (new.product_code,new.promotion_id,new.quota,new.active, current_timestamp,current_timestamp);
      RETURN NEW;
   END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER upsert_quota_logs AFTER INSERT OR UPDATE ON aloha_promotion_quota_new
FOR EACH ROW EXECUTE PROCEDURE insertlogquota();

