CREATE OR REPLACE FUNCTION insertlogquota() RETURNS TRIGGER AS $$
DECLARE
    prevProductCode VARCHAR(255);
    prevQuota INTEGER;
    prevActive BOOLEAN;
    prevPromotionId INTEGER;
BEGIN
    SELECT product_code, quota, active, promotion_id FROM aloha_promotion_quota_new WHERE product_code = NEW.product_code AND promotion_id = NEW.promotion_id
    INTO prevProductCode, prevQuota, prevActive, prevPromotionId;
    UPDATE aloha_promotion_quota_new SET 
        quota  = prevQuota - new.quantity_order,
        active = CASE WHEN prevQuota - new.quantity_order > 0 THEN TRUE WHEN prevQuota - new.quantity_order <= 0 THEN FALSE END,
        updated_at = now()
        WHERE aloha_promotion_quota_new.promotion_id = NEW.promotion_id AND aloha_promotion_quota_new.product_code = NEW.product_code;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER upsert_quota_logs AFTER INSERT ON aloha_promotion_quota_new_logs
FOR EACH ROW EXECUTE PROCEDURE insertlogquota();