CREATE TABLE IF NOT EXISTS aloha_promotion_sources (
    id SERIAL PRIMARY KEY NOT NULL,
    name VARCHAR(255) NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS aloha_promotion_sources_name_unique_idx ON aloha_promotion_sources (name);

INSERT INTO aloha_promotion_sources (name) VALUES
    ('MARKETING'),
    ('CUSTOMER_SERVICE'),
    ('HAPPYDATA'),
    ('INTERNAL'),
    ('BANK'),
    ('PARTNER'),
    ('HAPPYCORPORATE');
