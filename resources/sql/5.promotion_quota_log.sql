-- name: insert-promotion-quota-logs
INSERT INTO aloha_promotion_quota_new_logs (promotion_id, product_code, quota, active,created_at,updated_at,no_order,quantity_order)
VALUES ($1 , $2, $3, $4,now(),now(),$5,$6)

-- name: insert-promotion-quota-logs-by-promotion-code
INSERT INTO aloha_promotion_quota_new_logs (promotion_id, product_code, quota, active,created_at,updated_at,no_order,quantity_order)
VALUES ( (select id from aloha_promotions where aloha_promotions.promotion_code = $1) , $2, $3, $4,now(),now(),$5,$6)

-- name: get-promotion-quota-logs-by-product-code-noorder
SELECT product_code,promotion_id,SUM (quantity_order) AS total FROM aloha_promotion_quota_new_logs where no_order = $1 and product_code = $2 GROUP BY product_code, promotion_id ORDER BY total;	

-- name: get-all-product-code-by-no-order
SELECT product_code FROM aloha_promotion_quota_new_logs where no_order = $1

-- name: get-all-logs-by-product-code-and-no-order
SELECT product_code,promotion_id,no_order,quantity_order FROM aloha_promotion_quota_new_logs where no_order = $1 and product_code not in ($2) order by name asc

-- name : get-sum-of-quantity-more-than-zero
SELECT product_code,promotion_id,SUM (quantity_order) AS total FROM aloha_promotion_quota_new_logs where no_order = $1 and product_code not in (%s) GROUP BY product_code, promotion_id HAVING SUM (quantity_order) > 0 ORDER BY total;