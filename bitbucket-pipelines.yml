image: golang:1.18

definitions:
  steps:
    - step: &ecs_deploy
        name: Build with Dock<PERSON>, upload to ECR, update ECS task
        image: hfcoramil/pipeline-builder
        caches:
          - docker
        services:
          - docker
        script:
          - source /builder/scripts/ecs-deploy-lib.sh
          - login_aws ${ECS_ACCESS_KEY_ID} ${ECS_SECRET_ACCESS_KEY}
          - login_docker_to_aws_ecr ${ECS_REGION} ${CONTAINER_REPOSITORY_URL}
          - FULL_IMAGE_REPO_NAME="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
          - DEPLOY_DOCKER_IMAGE_URL=${DOCKER_IMAGE_URL} make docker-build-bitbucket
          - docker_tag_and_push ${DOCKER_IMAGE_URL} "${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:latest"
          - docker_tag_and_push ${DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
          - update_task_definition ${AWS_SERVICE_NAME} ${AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${ECS_REGION} 0    
    - step: &ecs_deploy_worker
        name: Build with Docker, upload to ECR, update ECS task
        image: hfcoramil/pipeline-builder
        caches:
          - docker
        services:
          - docker
        script:
          - source /builder/scripts/ecs-deploy-lib.sh
          - login_aws ${ECS_ACCESS_KEY_ID} ${ECS_SECRET_ACCESS_KEY}
          - login_docker_to_aws_ecr ${ECS_REGION} ${CONTAINER_REPOSITORY_URL}
          - FULL_IMAGE_REPO_NAME="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
          - DEPLOY_DOCKER_IMAGE_URL=${DOCKER_IMAGE_URL} make docker-promotion-worker-build-bitbucket
          - docker_tag_and_push ${DOCKER_IMAGE_URL} "${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:latest"
          - docker_tag_and_push ${DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
          - update_task_definition ${AWS_SERVICE_NAME} ${AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${ECS_REGION} 0
  services:
    docker:
      # Memory docker-in-docker
      memory: 2048
pipelines:
  default:
    - step:
        script: &DEFAULT_SCRIPT
          - go clean --modcache
          - export GOSUMDB=off
          - go install github.com/jteeuwen/go-bindata/go-bindata@latest
          - make bindata 
          - go mod tidy
          - go mod download
  custom:
    deploy_to_ecs_sandbox:
      - step: 
          <<: *ecs_deploy
          deployment: sandbox
    deploy_to_ecs_staging:
      - step:
          <<: *ecs_deploy
          deployment: staging
    deploy_to_ecs_production:
      - step:
          <<: *ecs_deploy
          deployment: production
          script:
            - source /builder/scripts/ecs-deploy-lib.sh
            - login_aws ${ECS_ACCESS_KEY_ID} ${ECS_SECRET_ACCESS_KEY}
            - login_docker_to_aws_ecr ${ECS_REGION} ${CONTAINER_REPOSITORY_URL}
            - FULL_IMAGE_REPO_NAME="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
            - DEPLOY_DOCKER_IMAGE_URL=${DOCKER_IMAGE_URL} make docker-build-bitbucket
            - docker_tag_and_push ${DOCKER_IMAGE_URL} "${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:latest"
            - docker_tag_and_push ${DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
            - update_task_definition ${AWS_SERVICE_NAME} ${AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${ECS_REGION} 0
            - update_task_definition ${AWS_SERVICE_NAME_SIRIUS} ${AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${ECS_REGION} 0
    deploy_worker_to_ecs_staging:
      - step:
          <<: *ecs_deploy_worker
          deployment: staging-worker
    deploy_worker_to_ecs_production:
      - step:
          <<: *ecs_deploy_worker
          deployment: production-worker
    deploy_to_production:
      - step:
          script:
            - apt-get update && apt-get -y -qq install zip tar
            - curl https://raw.githubusercontent.com/golang/dep/master/install.sh | sh
            - PACKAGE_PATH="${GOPATH}/src/happyfresh.io/${BITBUCKET_REPO_SLUG}"
            - mkdir -pv "${PACKAGE_PATH}"
            - tar -cO --exclude-vcs --exclude=bitbucket-pipelines.yml . | tar -xv -C "${PACKAGE_PATH}"
            - cd "${PACKAGE_PATH}"
            - dep ensure
            - BRANCH=master AWS_ACCESS_KEY_ID=${PROD_AWS_ACCESS_KEY} AWS_SECRET_ACCESS_KEY=${PROD_AWS_SECRET_ACCESS_KEY} AWS_BUCKET=${PROD_AWS_BUCKET} make bindata build package deploy
  branches:
    develop:
      - step:
          script:
            - curl https://raw.githubusercontent.com/golang/dep/master/install.sh | sh
            - PACKAGE_PATH="${GOPATH}/src/happyfresh.io/${BITBUCKET_REPO_SLUG}"
            - mkdir -pv "${PACKAGE_PATH}"
            - tar -cO --exclude-vcs --exclude=bitbucket-pipelines.yml . | tar -xv -C "${PACKAGE_PATH}"
            - cd "${PACKAGE_PATH}"
            - dep ensure
            - make bindata build package deploy
    sandbox/*:
      - step:
          deployment: Sandbox
          script:
            - curl https://raw.githubusercontent.com/golang/dep/master/install.sh | sh
            - PACKAGE_PATH="${GOPATH}/src/happyfresh.io/${BITBUCKET_REPO_SLUG}"
            - mkdir -pv "${PACKAGE_PATH}"
            - tar -cO --exclude-vcs --exclude=bitbucket-pipelines.yml . | tar -xv -C "${PACKAGE_PATH}"
            - cd "${PACKAGE_PATH}"
            - dep ensure
            - BRANCH=sandbox make bindata build package deploy
